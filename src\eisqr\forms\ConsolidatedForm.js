import React, { useEffect, useRef, useState } from "react";
import { Panel } from "primereact/panel";
import { InputTextarea } from "primereact/inputtextarea";
import useForceUpdate from "use-force-update";
import { Checkbox } from "primereact/checkbox";
import { InputText } from "primereact/inputtext";
import { Chips } from "primereact/chips";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";
import { Dropdown } from "primereact/dropdown";
import { Dialog } from "primereact/dialog";
import { Toast } from "primereact/toast";
import { useHistory, useLocation, useParams } from "react-router-dom";
import { MultiSelect } from 'primereact/multiselect';
import { Editor } from "primereact/editor";
import { ContextMenu } from "primereact/contextmenu";
import Swal from "sweetalert2";
import Axios from "axios";
import { API } from "../../constants/api_url";
import { useSelector } from "react-redux";
import moment from "moment";
import { InputNumber } from "primereact/inputnumber";
import { type } from "jquery";
import APIServices from "../../service/APIService";
import { Accordion, AccordionTab } from "primereact/accordion";
import { Chip } from "@mui/material";



const ConsolidateForm = () => {
    let yOffset = 0;
    const [activecell, setActiveCell] = useState({ data: {} });
    const [activecellId, setActiveCellId] = useState(null);
    const selector = useSelector((state) => state.user.admindetail)
    const [dupids, setDupIds] = useState([])
    const params = useLocation()
    const [form, setForm] = useState({ title: '', data1: [], comments: '', suffix: 'SRF', type: 1, formType: 1 })
    const [clienttag, setClientTag] = useState([])
    const [frameworkNames, setFrameworkNames] = useState([])

    const navigate = useHistory()
    const [cellconfigdialog, setCellConfigDialog] = useState(false)
    const [submitted, setSubmitted] = useState(false)
    const forceUpdate = useForceUpdate();
    const toast = useRef(null);
    const dragInProgress = useRef(false);
    const scrollContainerRef = useRef(null);
    const [draggedItem, setDraggedItem] = useState(null);
    const [draggedWidget, setDraggedWidget] = useState(null);
    const [dragOverIndex, setDragOverIndex] = useState(null);
    const [isDragging, setIsDragging] = useState(false);
    const [draggedElement, setDraggedElement] = useState(null);
    const [dropPosition, setDropPosition] = useState(null);
    const [cursorY, setCursorY] = useState(0);




    const typelist = [{ name: 'Text', id: 1 }, { name: 'TextArea', id: 2 }, { name: 'Number', id: 3 }, { name: 'Dropdown', id: 4 }, { name: 'Date', id: 6 }]

    function findMaxNumber(stringArray, startingString) {
        // Filter strings starting with the specified startingString
        const filteredStrings = stringArray.filter(str => str.startsWith(startingString));

        if (filteredStrings.length === 0) {
            return startingString + form.id + '_' + 1
        }
        // Extract numbers after the startingString and convert them to an array of numbers
        const numbersArray = filteredStrings.map(str => parseInt(str.split('_')[1]));

        // Find the maximum value in the numbersArray
        const maxValue = Math.max(...numbersArray) + 1;
        console.log(maxValue)
        return startingString + form.id + '_' + maxValue;
    }
    // function findMaxNumber(stringArray, startingString) {

    //     const filteredStrings = stringArray.filter(str => str.startsWith(startingString));

    //     if (filteredStrings.length === 0) {
    //         return startingString + form.id + '_' + 1
    //     }

    //     const numbersArray = filteredStrings.map(str => parseInt(str.split('_')[1]));


    //     const maxValue = Math.max(...numbersArray) + 1;
    //     console.log(maxValue)
    //     return startingString + form.id + '_' + maxValue;
    // }
    function extractSubstringFromSecondPosition(inputString, character) {
        const firstIndex = inputString.indexOf(character);
        const secondIndex = inputString.indexOf(character, firstIndex + 1);

        if (firstIndex !== -1 && secondIndex !== -1) {
            const startIndex = secondIndex + 1; // Starting from the second position after the second occurrence
            return parseInt(inputString.substring(startIndex));
        }

        return 1; // Character not found or only one occurrence
    }
    function findMaxNumber_table(stringArray, startingString) {
        // Filter strings starting with the specified startingString
        console.log(stringArray, startingString)
        const filteredStrings = stringArray.filter(str => str.startsWith(startingString));

        if (filteredStrings.length === 0) {
            return 1
        }
        // Extract numbers after the startingString and convert them to an array of numbers
        const numbersArray = filteredStrings.map(str => parseInt(str.split('_')[2]));

        // Find the maximum value in the numbersArray
        const maxValue = Math.max(...numbersArray) + 1;
        console.log(maxValue)
        return maxValue;
    }
    const getID = () => {
        console.log(form)
        if (form.data1.length !== 0) {
            let ids = []
            form.data1.forEach((i) => {
                if (i.type !== 'htmleditor' && i.type !== 'paragraph') {
                    if (i.type !== 'table' && i.type !== 'tableadd') {
                        console.log(i)
                        ids.push(i.name)
                    }
                }
            })
            console.log(ids)
            return findMaxNumber(ids, 'SDP')
        } else {
            return 'SDP' + form.id + '_1'
        }
    }
    const getID_table = (id) => {

        if (form.data1.length !== 0) {
            let ids = []
            form.data1.forEach((i) => {

                if ((i.type === 'table' || i.type == 'tableadd') && i.name === id) {
                    i.data.forEach((j) => {
                        Object.values(j).forEach((k) => {
                            ids.push(k.data.name)
                        })

                    })

                }

            })
            console.log(ids, findMaxNumber_table(ids, id), 'table')
            return findMaxNumber_table(ids, id)
        } else {
            return 1
        }
    }

    // Generate SDPTH IDs for table headers
    const getSDPTH_ID = () => {
        console.log('Generating SDPTH ID for form:', form.id)
        if (form.data1.length !== 0) {
            let ids = []
            form.data1.forEach((i) => {
                if (i.type === 'table' || i.type === 'tableadd') {
                    // Collect all existing SDPTH IDs from headerIds arrays
                    if (i.headerIds && Array.isArray(i.headerIds)) {
                        ids.push(...i.headerIds)
                    }
                }
            })
            console.log('Existing SDPTH IDs:', ids)
            return findMaxNumber_SDPTH(ids, 'SDPTH' + form.id)
        } else {
            return 'SDPTH' + form.id + '_1'
        }
    }

    // Helper function to find max number for SDPTH IDs
    function findMaxNumber_SDPTH(stringArray, startingString) {
        console.log('Finding max SDPTH number:', stringArray, startingString)
        const filteredStrings = stringArray.filter(str => str.startsWith(startingString));

        if (filteredStrings.length === 0) {
            return startingString + '_1'
        }
        // Extract numbers after the startingString and convert them to an array of numbers
        const numbersArray = filteredStrings.map(str => parseInt(str.split('_')[1]));

        // Find the maximum value in the numbersArray
        const maxValue = Math.max(...numbersArray) + 1;
        console.log('Next SDPTH number:', maxValue)
        return startingString + '_' + maxValue;
    }

    const getObjectByType = (type) => {
        switch (type) {
            case "text":
                return { type: "text", label: "", name: getID(), required: false, value: null }

                break;
            case "inputtextarea":
                return { type: "textarea", label: "", name: getID(), required: false, value: null }

                break;
            case "date":
                return { type: "date", label: "", name: getID(), required: false, value: null }

                break;
            case "file":
                return { type: "file", label: "", name: getID(), required: false, value: null }

                break;
            case "paragraph":
                return { type: "paragraph", label: "" }

                break;
            case "htmleditor":
                return { type: "htmleditor", label: "", value: null }

                break;

            case "number":
                return { type: "number", label: "", name: getID(), required: false, value: null, fraction: 0, directionsign: 0 }

                break;
            case "table":
                return { type: "table", label: "", name: getID(), value: [], headerlimit: 10, required: false, data: [], headerIds: [] }

                break;
            case "tableadd":
                return { type: "tableadd", label: "", name: getID(), value: [], maxrowlimit: 5, required: false, data: [], headerIds: [] }

                break;
            case "radio":
                return { type: "radio-group", name: getID(), label: "", values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }], required: false, data: [], value: null }

                break;
            case "checkbox":
                return { type: "checkbox-group", name: getID(), label: "", values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }, { label: 'Option-3', value: '3', selected: false }], required: false, value: null }

                break;
            case "checkpoint":
                return { type: "checkpoint", name: getID(), label: "", values: [{ label: 'Yes', value: '1', selected: false }, { label: 'No', value: '2', selected: false }, { label: 'NA', value: '3', selected: false }], required: false, value: null }

                break;
            case "dropdown":
                return { type: "select", label: "", name: getID(), values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }, { label: 'Option-3', value: '3', selected: false }], required: false, value: null }

                break;
            default:
                return null;
        }
    }
    const addWidget = (type) => {
        let loc = JSON.parse(JSON.stringify(form));
        setSubmitted(false)
        switch (type) {
            case "text":
                loc.data1.push({
                    type: "text", label: "", name: getID(), required: false, value: null,
                    isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "inputtextarea":
                loc.data1.push({
                    type: "textarea", label: "", name: getID(), required: false, value: null,
                    isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "date":
                loc.data1.push({
                    type: "date", label: "", name: getID(), required: false, value: null,
                    isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "file":
                loc.data1.push({
                    type: "file", label: "", name: getID(), required: false, value: null,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "paragraph":
                loc.data1.push({
                    type: "paragraph", label: "",
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "htmleditor":
                loc.data1.push({
                    type: "htmleditor", label: "", value: null,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;

            case "number":
                loc.data1.push({
                    type: "number", label: "", name: getID(), required: false, fraction: 0, value: null, directionsign: 0,
                    isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "table":
                loc.data1.push({
                    type: "table", label: "", name: getID(), value: [], headerlimit: 10, required: false, data: [], headerIds: [],
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "tableadd":
                loc.data1.push({
                    type: "tableadd", label: "", name: getID(), value: [], maxrowlimit: 5, required: false, data: [], headerIds: [],
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "radio":
                loc.data1.push({
                    type: "radio-group", name: getID(), label: "", values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }],
                    required: false, data: [], value: null, isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "checkbox":
                loc.data1.push({
                    type: "checkbox-group", name: getID(), label: "", values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }, { label: 'Option-3', value: '3', selected: false }],
                    required: false, value: null, isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "checkpoint":
                loc.data1.push({
                    type: "checkpoint", name: getID(), label: "", values: [{ label: 'Yes', value: '1', selected: false }, { label: 'No', value: '2', selected: false }, { label: 'NA', value: '3', selected: false }],
                    required: false, value: null, isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            case "dropdown":
                loc.data1.push({
                    type: "select", label: "", name: getID(), values: [{ label: 'Option-1', value: '1', selected: false }, { label: 'Option-2', value: '2', selected: false }, { label: 'Option-3', value: '3', selected: false }],
                    required: false, value: null, isAttachmentMandatory: false, isLegalCompliance: false, isDedicated: false,
                    framework: [], assignedFramework: {},
                    framework_mandatory_if_material: [], assignedFramework_mandatory_if_material: {},
                    framework_good_to_have: [], assignedFramework_good_to_have: {},
                    framework_not_required: [], assignedFramework_not_required: {}
                });
                setForm(loc);
                forceUpdate();
                break;
            default:
                return null;
        }
    };
    // Cleanup drag styles on component unmount or escape key
    useEffect(() => {
        const handleEscape = (e) => {
            if (e.key === 'Escape' && isDragging) {
                resetDragStyles();
            }
        };

        const handleMouseUp = () => {
            if (isDragging) {
                // Small delay to allow drop events to complete
                setTimeout(() => {
                    resetDragStyles();
                }, 100);
            }
        };

        document.addEventListener('keydown', handleEscape);
        document.addEventListener('mouseup', handleMouseUp);

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.removeEventListener('mouseup', handleMouseUp);
            resetDragStyles(); // Cleanup on unmount
        };
    }, [isDragging]);

    useEffect(() => {
        APIServices.get(API.UserProfile).then((res) => {
            let locuser = [];

            res.data.forEach((item) => {
                if (item.role === "clientadmin" && item.information.enterpriseid !== undefined) {
                    locuser.push({
                        name: item.information.enterpriseid,
                        id: item.id,
                    });
                }
            });

            setClientTag(locuser);
        });

        APIServices.get(API.Report_Name_Twos).then((res) => {
            setFrameworkNames(res.data);

            console.log(res);

        });
        console.log(params)
        if (params.state !== null && params.state !== undefined) {
            let loc = { ...params.state }
            console.log(loc)
            // loc.data1 = JSON.parse(loc.data1)

            // Initialize new framework properties for existing forms
            if (loc.data1 && Array.isArray(loc.data1)) {
                loc.data1.forEach(widget => {
                    // Initialize framework properties if they don't exist
                    if (widget.framework === undefined) {
                        widget.framework = [];
                    }
                    if (widget.assignedFramework === undefined) {
                        widget.assignedFramework = {};
                    }
                    if (widget.framework_mandatory_if_material === undefined) {
                        widget.framework_mandatory_if_material = [];
                    }
                    if (widget.assignedFramework_mandatory_if_material === undefined) {
                        widget.assignedFramework_mandatory_if_material = {};
                    }
                    if (widget.framework_good_to_have === undefined) {
                        widget.framework_good_to_have = [];
                    }
                    if (widget.assignedFramework_good_to_have === undefined) {
                        widget.assignedFramework_good_to_have = {};
                    }
                    if (widget.framework_not_required === undefined) {
                        widget.framework_not_required = [];
                    }
                    if (widget.assignedFramework_not_required === undefined) {
                        widget.assignedFramework_not_required = {};
                    }
                });
            }

            setForm(loc)

        }
    }, [])
    const addRow = (val, index) => {
        let obj = {}
        let id = getID_table(form.data1[index].name)

        val.forEach((key, ind) => {
            obj[key] = { type: 1, data: { name: form.data1[index].name + '_' + id, required: false } }
            id = id + 1
        })
        console.log(obj)
        form.data1[index].data.push(obj)
        forceUpdate()

    };
    const duplicateRowAtLast = (windex, rindex) => {
        let loc = JSON.parse(JSON.stringify(form))

        let selected_row = JSON.parse(JSON.stringify(loc.data1[windex].data[rindex]))
        let obj = {}
        let id = getID_table(form.data1[windex].name)
        Object.values(selected_row).forEach((item, j) => {
            console.log((Object.keys(selected_row)[j]))
            if (item.type !== 5) {
                item.required = false
                item.data['name'] = form.data1[windex].name + '_' + id
                id += 1
            } else {
                item.label = ''
            }
            obj[Object.keys(selected_row)[j]] = item
        })


        loc.data1[windex].data.push(obj);

        setForm(loc)
        forceUpdate()

    };
    const duplicateRowBelow = (windex, rindex) => {

        let loc = JSON.parse(JSON.stringify(form))
        let selected_row = JSON.parse(JSON.stringify(loc.data1[windex].data[rindex]))
        let obj = {}
        let id = getID_table(form.data1[windex].name)

        Object.values(selected_row).forEach((item, j) => {
            console.log((Object.keys(selected_row)[j]))
            if (item.type !== 5) {
                item.required = false
                item.data['name'] = form.data1[windex].name + '_' + id
                id += 1
            } else {
                item.label = ''
            }
            obj[Object.keys(selected_row)[j]] = item
        })

        loc.data1[windex].data.splice(rindex + 1, 0, obj);

        setForm(loc)

        forceUpdate()

    };
    const deleteTRow = (windex, rindex) => {

        form.data1[windex].data.splice(rindex, 1)
        forceUpdate()
    }
    const getCellClassName = (rowData) => {
        if (rowData.type !== 5) {

            if (rowData.data.name === undefined || rowData.data.name.trim().length === 0) {
                return 'cell-p-invalid'
            } else if (dupids.includes(rowData.data.name.trim().toLowerCase())) {
                return 'cell-p-invalid-id'
            }
            else {
                console.log(dupids.includes(rowData.data.name.trim().toLowerCase()), dupids)
                return ''
            }
        } else {
            return ''
        }
    }
    const actionTemplate = (rowData, windex, rindex) => {
        return (
            <>
                <Button
                    icon="pi pi-angle-down"
                    className="mr-2 actionbtn" style={{
                        width: '20px',
                        height: '20px',
                        background: 'transparent',
                        color: 'red'
                    }}
                    onClick={() => {
                        duplicateRowBelow(windex, rindex)
                    }}
                />
                <Button
                    icon="pi pi-angle-double-down"
                    className="mr-2 actionbtn" style={{
                        width: '20px',
                        height: '20px',
                        background: 'transparent',
                        color: 'red'
                    }}
                    onClick={() => {
                        duplicateRowAtLast(windex, rindex)
                    }}
                />
                <Button
                    icon="pi pi-trash"
                    className="mr-2 actionbtn" style={{
                        width: '20px',
                        height: '20px',
                        background: 'transparent',
                        color: 'palevioletred'
                    }}
                    onClick={() => {
                        deleteTRow(windex, rindex)
                    }}
                />
            </>
        )
    }
    const actionTemplate2 = (rowData, windex, rindex) => {
        return (
            <>

                <Button
                    icon="pi pi-trash"
                    className="mr-2 actionbtn" style={{
                        width: '20px',
                        height: '20px',
                        background: 'transparent',
                        color: 'palevioletred'
                    }}
                    onClick={() => {
                        deleteTRow(windex, rindex)
                    }}
                />
            </>
        )
    }
    const removeWidget = (index) => {
        let loc = JSON.parse(JSON.stringify(form));
        loc.data1.splice(index, 1)
        setForm(loc)
    }
    const editWidget = (index) => {
        let loc = JSON.parse(JSON.stringify(form));
        loc.data1.forEach((i, j) => {
            if (j === index) {
                i.expanded = i.expanded === undefined ? true : !i.expanded
            } else {
                i.expanded = false
            }
        })
        // loc[index].expanded = !loc[index].expanded
        setForm(loc)
    }
    const onMouseOver = (e, bg, clr) => {

        e.target.style.background = bg
        e.target.style.color = clr
        e.target.style.cursor = 'pointer'
    }
    const onMouseLeave = (e) => {

        e.target.style.background = 'transparent'
        e.target.style.color = 'rgba(0, 0, 0, 0.6)'
        e.target.style.cursor = 'none'
    }
    const checkLandN = (item) => {

        if (item.name !== undefined && item.name.trim().length !== 0 && !dupids.includes(item.name.trim().toLowerCase()) && checkDPName(item.name) && item.label.trim().length !== 0) {

            return false

        } else if (item.expanded === undefined || !item.expanded) {
            return true
        } else {
            return false
        }
    }
    const checkTable = (item) => {
        let check = false
        if (item.value.length === 0 || item.label.trim().length === 0) {
            check = true
        } else if (item.data.length !== 0) {
            Object.values(item.data).forEach((i) => {
                Object.values(i).forEach((j) => {
                    if (j.type !== 5) {
                        if (j.data.name !== undefined) {
                            if (dupids.includes(j.data.name.trim().toLowerCase())) {
                                check = true
                            }
                        } else {
                            check = true
                        }
                    }

                })
            })

        } else {
            check = true
        }

        return check
    }
    const checkLNO = (item) => {
        console.log(validateArray(item.values))
        if (item.name !== undefined && item.name.trim().length !== 0 && !dupids.includes(item.name.trim().toLowerCase()) && checkDPName(item.name) && item.label.trim().length !== 0 && validateArray(item.values).result) {

            return false

        } else if (item.expanded === undefined || !item.expanded) {
            return true
        } else {
            return false
        }
    }
    const checkCP = (item) => {
        console.log(item, item.values.filter((i) => { return i.label.toString().trim().length === 0 }))
        if (item.name !== undefined && item.name.trim().length !== 0 && !dupids.includes(item.name.trim().toLowerCase()) && checkDPName(item.name) && item.label.trim().length !== 0 && item.values.filter((i) => { return i.label.toString().trim().length === 0 }).length === 0) {
            return false
        } else if (item.expanded === undefined || !item.expanded) {
            return true
        } else {
            return false
        }
    }
    function isValueUniqueInArray(value, arr, key) {
        let count = 0;

        for (const item of arr) {
            if (item[key].toString().trim() === value.toString().trim()) {
                count++;
                if (count > 1) {
                    return false;
                }
            }
        }

        return count === 1;
    }
    const renderComponent = (widget, index) => {
        console.log(widget)
        switch (widget.type) {
            case "text":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragEnter={(e) => { console.log('enter', e) }} onDragLeave={(e) => { console.log('leave', e) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { itemDragOver(e) }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative', position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted & checkLandN(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label '}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"text" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />


                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="col-12">
                                <label  >
                                    Placeholder
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.placeholder}
                                    onChange={(e) => {
                                        form.data1[index].placeholder = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Text</label>
                        </div>
                    </div>
                );
            case "number":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted & checkLandN(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"number" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Fraction
                                </label>
                                <InputNumber
                                    min={0}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.fraction}
                                    className={(widget.fraction < 0 || widget.fraction === null || widget.fraction === undefined) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].fraction = e.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Does a higher value represent improvement, or does a lower value represent improvement? (0-Higher, 1-Lower)
                                </label>
                                <InputNumber
                                    min={0}
                                    max={1}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.directionsign}
                                    className={(widget.directionsign < 0 || widget.directionsign === null || widget.directionsign === undefined) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].directionsign = e.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="col-12">
                                <label  >
                                    Placeholder
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.placeholder}
                                    onChange={(e) => {
                                        form.data1[index].placeholder = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Number</label>
                        </div>
                    </div>
                );
            case "date":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted & checkLandN(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"number" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Date</label>
                        </div>
                    </div>
                );
            case "file":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted & checkLandN(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"number" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}

                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>File</label>
                        </div>
                    </div>
                );

            case "paragraph":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && widget.label.trim().length === 0 && widget.expanded !== true) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>

                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Paragraph</label>
                        </div>
                    </div>
                );

            case "textarea":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted & checkLandN(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"textarea" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="col-12">
                                <label  >
                                    Placeholder
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.placeholder}
                                    onChange={(e) => {
                                        form.data1[index].placeholder = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>TextArea</label>
                        </div>
                    </div>
                );
            case "table":
                return (
                    <div>
                        <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                            <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: submitted && checkTable(widget) && widget.expanded !== true && '1px solid red' }}>
                                <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                    {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                                </div>
                                <div className="col-2 justify-content-end flex">
                                    <i className="material-icons" style={{

                                        borderRadius: '5px',
                                        margin: 5,
                                        fontSize: '18px',
                                        padding: '2px'
                                    }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                    <i className="material-icons" style={{

                                        borderRadius: '5px',
                                        margin: 5,
                                        fontSize: '18px',
                                        padding: '2px'
                                    }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                                </div>
                            </div>
                            {widget.expanded && <>

                                <div className="col-12">
                                    <label  >
                                        Label
                                    </label>
                                    <InputTextarea
                                        type={"text"}
                                        style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                        value={widget.label}
                                        className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                        onChange={(e) => {
                                            form.data1[index].label = e.target.value;
                                            forceUpdate();
                                        }}
                                    />
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Help Text
                                    </label>
                                    <InputText
                                        type={"text"}
                                        style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                        value={widget.description}
                                        onChange={(e) => {
                                            form.data1[index].description = e.target.value;
                                            forceUpdate();
                                        }}
                                    />
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Headers ( comma separated and limit is not more than 10  )
                                    </label>
                                    <Chips

                                        onRemove={(e) => {
                                            console.log('Removing header:', e);

                                            // For table/tableadd, also remove corresponding SDPTH ID
                                            if (widget.type === 'table' || widget.type === 'tableadd') {
                                                const headerIndex = widget.value.indexOf(e.value[0])
                                                if (headerIndex !== -1 && widget.headerIds && widget.headerIds[headerIndex]) {
                                                    console.log('Removing SDPTH ID:', widget.headerIds[headerIndex])
                                                    widget.headerIds.splice(headerIndex, 1)
                                                }
                                            }

                                            widget.data.forEach((i) => { delete i[e.value[0]] });
                                            widget.data = widget.data.filter((i) => { return Object.keys(i).length !== 0 });
                                        }}
                                        placeholder={widget.expanded === true && submitted && widget.value.length > 11 && "add header"}
                                        value={widget.value}
                                        className={'col-12 chip-fw ' + (widget.expanded === true && submitted && widget.value.length === 0 && 'p-invalid')}
                                        onChange={(e) => {
                                            form.data1[index].value = e.value;

                                            forceUpdate();
                                        }}
                                        onAdd={(e, i) => {
                                            console.log('Adding header for table type:', widget.type)

                                            // For table and tableadd types, initialize SDPTH ID placeholder
                                            if (widget.type === 'table' || widget.type === 'tableadd') {
                                                // Add empty SDPTH ID placeholder to headerIds array
                                                if (!widget.headerIds) {
                                                    widget.headerIds = []
                                                }
                                                widget.headerIds.push('') // Empty placeholder for user to fill
                                                console.log('Added SDPTH ID placeholder for header:', e.value)
                                            }

                                            let id = getID_table(widget.name)
                                            console.log(id)
                                            if (widget.value.length !== 0) {
                                                widget.data.forEach((i, j) => {
                                                    i[e.value] = { type: 1, data: { name: widget.name + '_' + id, required: false } }
                                                    id += 1
                                                })

                                            } else {
                                                widget.data.push({ [e.value]: { type: 1, data: { name: widget.name + '_' + id, required: false } } })
                                            }

                                        }}
                                        max={10}
                                        separator=","
                                    />
                                </div>

                                {/* SDPTH ID Input Fields for Table/TableAdd */}
                                {(widget.type === 'table' || widget.type === 'tableadd') && widget.value.length > 0 && (
                                    <div className="col-12">
                                        <label style={{ fontWeight: 'bold', color: '#333', marginBottom: '10px', display: 'block' }}>
                                            SDPTH IDs for Headers (Format: SDPTH{form.id}_1, SDPTH{form.id}_2, etc.)
                                        </label>
                                        {widget.value.map((header, headerIndex) => (
                                            <div key={headerIndex} className="flex align-items-center" style={{ marginBottom: '8px' }}>
                                                <div className="col-3">
                                                    <label style={{ fontWeight: '500' }}>{header}:</label>
                                                </div>
                                                <div className="col-6">
                                                    <InputText
                                                        value={widget.headerIds && widget.headerIds[headerIndex] ? widget.headerIds[headerIndex] : ''}
                                                        onChange={(e) => {
                                                            if (!widget.headerIds) {
                                                                widget.headerIds = new Array(widget.value.length).fill('')
                                                            }
                                                            widget.headerIds[headerIndex] = e.target.value
                                                            forceUpdate()
                                                        }}
                                                        placeholder={`SDPTH${form.id}_${headerIndex + 1}`}
                                                        className={
                                                            (submitted && (!widget.headerIds || !widget.headerIds[headerIndex] || widget.headerIds[headerIndex].trim() === '')) ||
                                                            (widget.headerIds && widget.headerIds[headerIndex] && dupids.some(dup => dup.toLowerCase().includes(widget.headerIds[headerIndex].toLowerCase())))
                                                            ? 'p-invalid' : ''
                                                        }
                                                    />
                                                </div>
                                                <div className="col-3">
                                                    {submitted && (!widget.headerIds || !widget.headerIds[headerIndex] || widget.headerIds[headerIndex].trim() === '') && (
                                                        <small className="p-invalid" style={{ color: 'red' }}>
                                                            SDPTH ID required
                                                        </small>
                                                    )}
                                                    {widget.headerIds && widget.headerIds[headerIndex] && dupids.some(dup => dup.toLowerCase().includes(widget.headerIds[headerIndex].toLowerCase())) && (
                                                        <small className="p-invalid" style={{ color: 'red' }}>
                                                            Duplicate SDPTH ID
                                                        </small>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                        {submitted && widget.value.length !== (widget.headerIds ? widget.headerIds.length : 0) && (
                                            <small className="p-invalid" style={{ color: 'red', display: 'block', marginTop: '5px' }}>
                                                Number of headers ({widget.value.length}) must equal number of SDPTH IDs ({widget.headerIds ? widget.headerIds.length : 0})
                                            </small>
                                        )}
                                    </div>
                                )}

                                {widget.value.length !== 0 && (
                                    <div className="col-12">
                                        <div className="flex justify-content-end" style={{ margin: 10 }}>
                                            <Button onClick={() => { addRow(widget.value, index) }} icon='pi pi-plus'></Button>
                                        </div>

                                        <DataTable showGridlines reorderableRows onRowReorder={(e) => { form.data1[index].data = e.value; forceUpdate() }} value={widget.data} className="fullheight" style={{ width: '100%', margin: 10 }}>
                                            <Column rowReorder style={{ width: '3rem' }} />
                                            {widget.value.map((h, j) => {
                                                return <Column header={h} body={(rowData, props) => { console.log(rowData[h], props); return <div className={getCellClassName(rowData[h])} onClick={() => { setActiveCellId({ widgetind: index, dataind: props.rowIndex, colind: j }); setActiveCell(JSON.parse(JSON.stringify(rowData[h]))); setCellConfigDialog(true) }}>{rowData[h].type === 5 ? rowData[h].data.label : typelist.find((i) => { return i.id === rowData[h].type }).name}</div> }} />;
                                            })}
                                            <Column style={{ width: 112 }} header='Action' body={(rowData, props) => { return actionTemplate(rowData, index, props.rowIndex) }} />
                                        </DataTable>
                                    </div>
                                )}
                            </>}
                            <div style={{ position: 'absolute', top: 0, left: 0 }}>
                                <label style={{
                                    fontWeight: 'bold',
                                    textShadow: '1px 1px darkgrey',
                                    width: 100,
                                    background: 'white',
                                    display: 'flex',
                                    borderRadius: '10px',
                                    padding: '2px'
                                }}>Static Table</label>
                            </div>
                        </div>

                    </div>
                );
            case "tableadd":
                return (
                    <div>
                        <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                            <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: submitted && checkTable(widget) && widget.expanded !== true && '1px solid red' }}>
                                <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                    {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                                </div>
                                <div className="col-2 justify-content-end flex">
                                    <i className="material-icons" style={{

                                        borderRadius: '5px',
                                        margin: 5,
                                        fontSize: '18px',
                                        padding: '2px'
                                    }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                    <i className="material-icons" style={{

                                        borderRadius: '5px',
                                        margin: 5,
                                        fontSize: '18px',
                                        padding: '2px'
                                    }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                                </div>
                            </div>
                            {widget.expanded && <>
                                <div className="flex align-items-center col-5" style={{ margin: 10 }}>
                                    <Checkbox
                                        inputId={"cb" + index}
                                        value={widget.required}
                                        onChange={(e) => {
                                            form.data1[index].required = !e.value;
                                            forceUpdate();
                                        }}
                                        checked={widget.required}
                                    />
                                    <label htmlFor={"cb" + index} className="ml-2">
                                        is Required
                                    </label>
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Label
                                    </label>
                                    <InputTextarea
                                        type={"text"}
                                        style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                        value={widget.label}
                                        className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                        onChange={(e) => {
                                            form.data1[index].label = e.target.value;
                                            forceUpdate();
                                        }}
                                    />
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Maximum rows allowed (1-100)
                                    </label>
                                    <InputText
                                        type={'number'}
                                        style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                        value={widget.maxrowlimit}

                                        onChange={(e) => {
                                            console.log(e.target.value)
                                            form.data1[index].maxrowlimit = (parseInt(e.target.value) === 0 || e.target.value === '') ? 1 : parseInt(e.target.value) > 100 ? 100 : parseInt(e.target.value);
                                            forceUpdate();
                                        }}
                                    />
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Help Text
                                    </label>
                                    <InputText
                                        type={"text"}
                                        style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                        value={widget.description}
                                        onChange={(e) => {
                                            form.data1[index].description = e.target.value;
                                            forceUpdate();
                                        }}
                                    />
                                </div>
                                <div className="col-12">
                                    <label  >
                                        Headers ( comma separated and limit is not more than 10  )
                                    </label>
                                    <Chips

                                        onRemove={(e) => {
                                            console.log('Removing header:', e);

                                            // For table/tableadd, also remove corresponding SDPTH ID
                                            if (widget.type === 'table' || widget.type === 'tableadd') {
                                                const headerIndex = widget.value.indexOf(e.value[0])
                                                if (headerIndex !== -1 && widget.headerIds && widget.headerIds[headerIndex]) {
                                                    console.log('Removing SDPTH ID:', widget.headerIds[headerIndex])
                                                    widget.headerIds.splice(headerIndex, 1)
                                                }
                                            }

                                            widget.data.forEach((i) => { delete i[e.value[0]] });
                                            widget.data = widget.data.filter((i) => { return Object.keys(i).length !== 0 });
                                        }}
                                        placeholder={widget.expanded === true && submitted && widget.value.length > 11 && "add header"}
                                        value={widget.value}
                                        className={'col-12 chip-fw ' + (widget.expanded === true && submitted && widget.value.length === 0 && 'p-invalid')}
                                        onChange={(e) => {
                                            form.data1[index].value = e.value;

                                            forceUpdate();
                                        }}
                                        onAdd={(e) => {
                                            console.log('Adding header for tableadd type:', widget.type)

                                            // For table and tableadd types, initialize SDPTH ID placeholder
                                            if (widget.type === 'table' || widget.type === 'tableadd') {
                                                // Add empty SDPTH ID placeholder to headerIds array
                                                if (!widget.headerIds) {
                                                    widget.headerIds = []
                                                }
                                                widget.headerIds.push('') // Empty placeholder for user to fill
                                                console.log('Added SDPTH ID placeholder for header:', e.value)
                                            }

                                            let id = getID_table(widget.name)
                                            if (widget.value.length !== 0) {
                                                widget.data.forEach((i) => {
                                                    i[e.value] = { type: 1, data: { name: widget.name + '_' + id } }
                                                    id += 1
                                                })

                                            } else {
                                                widget.data.push({ [e.value]: { type: 1, data: { name: widget.name + '_' + id } } })
                                            }

                                        }}
                                        max={10}
                                        separator=","
                                    />
                                </div>

                                {/* SDPTH ID Input Fields for TableAdd */}
                                {(widget.type === 'table' || widget.type === 'tableadd') && widget.value.length > 0 && (
                                    <div className="col-12">
                                        <label style={{ fontWeight: 'bold', color: '#333', marginBottom: '10px', display: 'block' }}>
                                            SDPTH IDs for Headers (Format: SDPTH{form.id}_1, SDPTH{form.id}_2, etc.)
                                        </label>
                                        {widget.value.map((header, headerIndex) => (
                                            <div key={headerIndex} className="flex align-items-center" style={{ marginBottom: '8px' }}>
                                                <div className="col-3">
                                                    <label style={{ fontWeight: '500' }}>{header}:</label>
                                                </div>
                                                <div className="col-6">
                                                    <InputText
                                                        value={widget.headerIds && widget.headerIds[headerIndex] ? widget.headerIds[headerIndex] : ''}
                                                        onChange={(e) => {
                                                            if (!widget.headerIds) {
                                                                widget.headerIds = new Array(widget.value.length).fill('')
                                                            }
                                                            widget.headerIds[headerIndex] = e.target.value
                                                            forceUpdate()
                                                        }}
                                                        placeholder={`SDPTH${form.id}_${headerIndex + 1}`}
                                                        className={
                                                            (submitted && (!widget.headerIds || !widget.headerIds[headerIndex] || widget.headerIds[headerIndex].trim() === '')) ||
                                                            (widget.headerIds && widget.headerIds[headerIndex] && dupids.some(dup => dup.toLowerCase().includes(widget.headerIds[headerIndex].toLowerCase())))
                                                            ? 'p-invalid' : ''
                                                        }
                                                    />
                                                </div>
                                                <div className="col-3">
                                                    {submitted && (!widget.headerIds || !widget.headerIds[headerIndex] || widget.headerIds[headerIndex].trim() === '') && (
                                                        <small className="p-invalid" style={{ color: 'red' }}>
                                                            SDPTH ID required
                                                        </small>
                                                    )}
                                                    {widget.headerIds && widget.headerIds[headerIndex] && dupids.some(dup => dup.toLowerCase().includes(widget.headerIds[headerIndex].toLowerCase())) && (
                                                        <small className="p-invalid" style={{ color: 'red' }}>
                                                            Duplicate SDPTH ID
                                                        </small>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                        {submitted && widget.value.length !== (widget.headerIds ? widget.headerIds.length : 0) && (
                                            <small className="p-invalid" style={{ color: 'red', display: 'block', marginTop: '5px' }}>
                                                Number of headers ({widget.value.length}) must equal number of SDPTH IDs ({widget.headerIds ? widget.headerIds.length : 0})
                                            </small>
                                        )}
                                    </div>
                                )}

                                {widget.value.length !== 0 && (
                                    <div className="col-12">
                                        <div className="flex justify-content-end" style={{ margin: 10 }}>

                                            {widget.data.length === 0 && <Button onClick={() => { addRow(widget.value, index) }} icon='pi pi-plus'></Button>}
                                        </div>

                                        <DataTable showGridlines value={widget.data} className="fullheight" style={{ width: '100%', margin: 10 }}>

                                            {widget.value.map((h, j) => {
                                                return <Column header={h} body={(rowData, props) => { console.log(rowData[h], props); return <div className={getCellClassName(rowData[h])} onClick={() => { setActiveCellId({ widgetind: index, dataind: props.rowIndex, colind: j }); setActiveCell(JSON.parse(JSON.stringify(rowData[h]))); setCellConfigDialog(true) }}>{rowData[h].type === 5 ? rowData[h].data.label : typelist.find((i) => { return i.id === rowData[h].type }).name}</div> }} />;
                                            })}
                                            <Column style={{ width: 35 }} header='Action' body={(rowData, props) => { return actionTemplate2(rowData, index, props.rowIndex) }} />
                                        </DataTable>
                                    </div>
                                )}
                            </>}
                            <div style={{ position: 'absolute', top: 0, left: 0 }}>
                                <label style={{
                                    fontWeight: 'bold',
                                    textShadow: '1px 1px darkgrey',
                                    width: 105,
                                    background: 'white',
                                    display: 'flex',
                                    borderRadius: '10px',
                                    padding: '2px'
                                }}>Dynamic Table</label>
                            </div>
                        </div>
                    </div>
                );


            case "select":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && checkLNO(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"text" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="m-3 col-12">
                                <label htmlFor="ph" className="font-bold block mb-2">Options</label>
                                <div  >
                                    {widget.values.map((i, ind) => {

                                        return (
                                            <div className="col-12" style={{ padding: 5 }}>
                                                <div className="grid" style={{ alignItems: 'center' }}>
                                                    <div className="col-5" >

                                                        <InputText type="text" className={i.label.trim().length === 0 && 'p-invalid'} value={i.label} onChange={(e) => { form.data1[index].values[ind].label = e.target.value; forceUpdate(); }} placeholder="label" />
                                                    </div>

                                                    <div className="col-5">

                                                        <InputText type="text" className={(i.value.toString().trim().length === 0 || !isValueUniqueInArray(i.value, form.data1[index].values, 'value')) && 'p-invalid'} value={i.value} onChange={(e) => { form.data1[index].values[ind].value = e.target.value; forceUpdate(); }} placeholder="value" />


                                                    </div>

                                                    <div className="col-2">
                                                        {((form.data1[index].values.length - 1 === ind) || (ind === 1 && form.data1[index].values.length === 2)) ?
                                                            <div>
                                                                <i className="material-icons" onClick={(e) => { e.stopPropagation(); addNewRow(form.data1[index].values) }} style={{ color: 'green' }}>add_box</i>
                                                                {ind > 1 && <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>}

                                                            </div> :
                                                            ind > 1 &&
                                                            <div>
                                                                <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>

                                                            </div>

                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>

                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Dropdown</label>
                        </div>
                    </div>
                );

            case "checkbox-group":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && checkLNO(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"text" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="m-3 col-12">
                                <label htmlFor="ph" className="font-bold block mb-2">Options</label>
                                <div  >
                                    {widget.values.map((i, ind) => {

                                        return (
                                            <div className="col-12" style={{ padding: 5 }}>
                                                <div className="grid" style={{ alignItems: 'center' }}>
                                                    <div className="col-5" >

                                                        <InputText type="text" value={i.label} className={i.label.trim().length === 0 && 'p-invalid'} onChange={(e) => { form.data1[index].values[ind].label = e.target.value; forceUpdate(); }} placeholder="label" />
                                                    </div>

                                                    <div className="col-5">

                                                        <InputText type="text" value={i.value} className={(i.value.toString().trim().length === 0 || !isValueUniqueInArray(i.value, form.data1[index].values, 'value')) && 'p-invalid'} onChange={(e) => { form.data1[index].values[ind].value = e.target.value; forceUpdate(); }} placeholder="value" />


                                                    </div>

                                                    <div className="col-2">
                                                        {((form.data1[index].values.length - 1 === ind) || (ind === 1 && form.data1[index].values.length === 2)) ?
                                                            <div>
                                                                <i className="material-icons" onClick={(e) => { e.stopPropagation(); addNewRow(form.data1[index].values) }} style={{ color: 'green' }}>add_box</i>
                                                                {ind > 1 && <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>}

                                                            </div> :
                                                            ind > 1 &&
                                                            <div>
                                                                <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>

                                                            </div>

                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>

                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Checkbox</label>
                        </div>
                    </div>
                );

            case "radio-group":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && checkLNO(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"text" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="m-3 col-12">
                                <label htmlFor="ph" className="font-bold block mb-2">Options</label>
                                <div  >
                                    {widget.values.map((i, ind) => {

                                        return (
                                            <div className="col-12" style={{ padding: 5 }}>
                                                <div className="grid" style={{ alignItems: 'center' }}>
                                                    <div className="col-5" >

                                                        <InputText type="text" value={i.label} className={i.label.trim().length === 0 && 'p-invalid'} onChange={(e) => { form.data1[index].values[ind].label = e.target.value; forceUpdate(); }} placeholder="label" />
                                                    </div>

                                                    <div className="col-5">

                                                        <InputText type="text" className={(i.value.toString().trim().length === 0 || !isValueUniqueInArray(i.value, form.data1[index].values, 'value')) && 'p-invalid'} value={i.value} onChange={(e) => { form.data1[index].values[ind].value = e.target.value; forceUpdate(); }} placeholder="value" />


                                                    </div>

                                                    <div className="col-2">
                                                        {((form.data1[index].values.length - 1 === ind) || (ind === 1 && form.data1[index].values.length === 2)) ?
                                                            <div>
                                                                <i className="material-icons" onClick={(e) => { e.stopPropagation(); addNewRow(form.data1[index].values) }} style={{ color: 'green' }}>add_box</i>
                                                                {ind > 1 && <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>}

                                                            </div> :
                                                            ind > 1 &&
                                                            <div>
                                                                <i className="material-icons" onClick={() => { deleteDDRow(index, ind) }}>delete</i>

                                                            </div>

                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>

                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Radio</label>
                        </div>
                    </div>
                );

            case "checkpoint":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && checkCP(widget)) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? widget.label : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"text" + index}
                                    value={widget.required}
                                    onChange={(e) => {
                                        form.data1[index].required = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.required}
                                />
                                <label htmlFor={"cb" + index} className="ml-2">
                                    is Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"attman" + index}
                                    value={widget.isAttachmentMandatory}
                                    onChange={(e) => {
                                        form.data1[index].isAttachmentMandatory = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isAttachmentMandatory}
                                />
                                <label htmlFor={"attman" + index} className="ml-2">
                                    is Attachment Required
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"dedi" + index}
                                    value={widget.isDedicated}
                                    onChange={(e) => {
                                        form.data1[index].isDedicated = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isDedicated}
                                />
                                <label htmlFor={"dedi" + index} className="ml-2">
                                    is this Dedicated Question
                                </label>
                            </div>
                            <div className="flex align-items-center" style={{ margin: 10 }}>
                                <Checkbox
                                    inputId={"legal" + index}
                                    value={widget.isLegalCompliance}
                                    onChange={(e) => {
                                        form.data1[index].isLegalCompliance = !e.value;
                                        forceUpdate();
                                    }}
                                    checked={widget.isLegalCompliance}
                                />
                                <label htmlFor={"legal" + index} className="ml-2">
                                    is this Legal Compliance
                                </label>
                            </div>
                            <FrameworkComponent index={index} forceUpdate={forceUpdate} form={form} widget={widget} frameworkList={frameworkNames} />

                            <div className="col-12">
                                <label  >
                                    Label
                                </label>
                                <InputTextarea
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.label}
                                    className={widget.label.trim().length === 0 && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].label = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            <div className="col-12">
                                <label  >
                                    ID
                                </label>
                                <InputText
                                    type={"text"}

                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.name}
                                    className={((widget.name === undefined || widget.name.trim().length === 0) || (dupids.includes(widget.name.trim().toLowerCase())) || !checkDPName(widget.name.trim())) && submitted && widget.expanded && 'p-invalid'}
                                    onChange={(e) => {
                                        form.data1[index].name = e.target.value.trim();
                                        forceUpdate();
                                    }}
                                />
                            </div>
                            {widget.name !== undefined && (dupids.includes(widget.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                            <div className="m-3 col-12">
                                <label htmlFor="ph" className="font-bold block mb-2">Options</label>
                                <div  >
                                    {widget.values.map((i, ind) => {

                                        return (
                                            <div className="col-12" style={{ padding: 5 }}>
                                                <div className="grid" style={{ alignItems: 'center' }}>
                                                    <div className="col-5" >

                                                        <InputText type="text" className={i.label.trim().length === 0 && 'p-invalid'} value={i.label} onChange={(e) => { form.data1[index].values[ind].label = e.target.value; forceUpdate(); }} placeholder="label" />
                                                    </div>




                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>

                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>Checkpoint</label>
                        </div>
                    </div>
                );
            case "htmleditor":
                return (
                    <div onDrop={(e) => { itemDrop(e, index) }} onDragStart={(e) => { itemDragStart(e, widget) }} onDragOver={(e) => { e.preventDefault() }} onDragEnd={(e) => { itemDragEnd(e, index) }} className="flex flex-wrap gap-3 p-card " draggable={widget.expanded !== true} style={{ borderRadius: 10, margin: 5, padding: 5, position: 'relative' }}>
                        <div className="col-12 " style={{ borderRadius: 10, background: '#00000010', color: '#00000099', display: 'flex', border: (submitted && widget.expanded !== true && widget.label.trim().length === 0) ? '1px solid red' : '' }}>
                            <div className="col-10" style={{ alignItems: 'center', display: 'flex' }}>
                                {(widget.label.trim().length !== 0) ? 'Click edit to open' : 'please provide label'}
                            </div>
                            <div className="col-2 justify-content-end flex">
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'green', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { editWidget(index) }}>edit</i>
                                <i className="material-icons" style={{

                                    borderRadius: '5px',
                                    margin: 5,
                                    fontSize: '18px',
                                    padding: '2px'
                                }} onMouseOver={(e) => { onMouseOver(e, 'red', 'white') }} onMouseLeave={(e) => { onMouseLeave(e) }} onClick={() => { removeWidget(index) }}>close</i>
                            </div>
                        </div>
                        {widget.expanded && <>

                            <div className="col-12">
                                <label  >
                                    HTML Editor
                                </label>
                                <Editor value={widget.label} onTextChange={(e) => {
                                    form.data1[index].label = e.htmlValue === null ? '' : e.htmlValue;
                                    forceUpdate();
                                }} style={{ width: '100%', padding: 10, maxHeight: 350, height: 'auto', overflow: 'scroll', border: (widget.label.trim().length === 0 && submitted && widget.expanded) ? '1px solid red' : '' }} onChange={(e) => console.log(e)} />


                            </div>
                            <div className="col-12">
                                <label  >
                                    Help Text
                                </label>
                                <InputText
                                    type={"text"}
                                    style={{ width: "100%", margin: '10px 0px 10px 0px' }}
                                    value={widget.description}
                                    onChange={(e) => {
                                        form.data1[index].description = e.target.value;
                                        forceUpdate();
                                    }}
                                />
                            </div>
                        </>}
                        <div style={{ position: 'absolute', top: 0, left: 0 }}>
                            <label style={{
                                fontWeight: 'bold',
                                textShadow: '1px 1px darkgrey',
                                width: 100,
                                background: 'white',
                                display: 'flex',
                                borderRadius: '10px',
                                padding: '2px'
                            }}>HTML Editor</label>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };
    const addNewRow = (options) => {
        options.push({ label: '', value: (options.length + 1).toString() })
        forceUpdate()
        console.log(options)
    }
    const deleteRow = (index) => {
        activecell.data.values.splice(index, 1)
        forceUpdate()
    }
    const deleteDDRow = (index1, index2) => {
        form.data1[index1].values.splice(index2, 1)
        forceUpdate()
    }
    // Reset all drag-related styles and states
    const resetDragStyles = () => {
        // Get stack trace to see what's calling this
        console.log('🔄 Resetting drag styles - Called from:', new Error().stack);

        // Only reset if drag is actually finished
        if (dragInProgress.current) {
            console.log('⚠️ WARNING: Attempted to reset during active drag - IGNORING');
            return;
        }

        console.log('✅ Proceeding with reset');

        // Reset all draggable widgets immediately
        document.querySelectorAll('.draggable-widget').forEach(element => {
            element.style.opacity = '';
            element.style.transform = '';
            element.style.boxShadow = '';
        });

        // Reset all form widgets
        document.querySelectorAll('.p-card').forEach(element => {
            element.style.opacity = '';
            element.style.transform = '';
            element.style.boxShadow = '';
        });

        // Reset drop zones
        document.querySelectorAll('.drop-zone-active').forEach(element => {
            element.classList.remove('drop-zone-active');
        });

        // Reset states immediately
        setDraggedItem(null);
        setDraggedWidget(null);
        setDragOverIndex(null);
        setIsDragging(false);
        setDraggedElement(null);
        setDropPosition(null);

        console.log('✅ Drag styles reset complete');
    };

    // Force reset after successful drop
    const forceResetAfterDrop = () => {
        console.log('🎯 Force reset after successful drop');

        // Clear the global flag first
        dragInProgress.current = false;

        // Reset all draggable widgets immediately
        document.querySelectorAll('.draggable-widget').forEach(element => {
            element.style.opacity = '';
            element.style.transform = '';
            element.style.boxShadow = '';
        });

        // Reset all form widgets
        document.querySelectorAll('.p-card').forEach(element => {
            element.style.opacity = '';
            element.style.transform = '';
            element.style.boxShadow = '';
        });

        // Reset drop zones
        document.querySelectorAll('.drop-zone-active').forEach(element => {
            element.classList.remove('drop-zone-active');
        });

        // Reset states immediately
        setDraggedItem(null);
        setDraggedWidget(null);
        setDragOverIndex(null);
        setIsDragging(false);
        setDraggedElement(null);
        setDropPosition(null);

        console.log('✅ Force reset complete');
    };

    // Auto-scroll functionality during drag
    const handleAutoScroll = (e) => {
        if (!scrollContainerRef.current || !isDragging) return;

        const container = scrollContainerRef.current;
        const rect = container.getBoundingClientRect();
        const scrollThreshold = 50; // pixels from edge to trigger scroll
        const scrollSpeed = 10; // pixels per scroll

        // Check if cursor is near top or bottom of container
        const cursorY = e.clientY - rect.top;

        if (cursorY < scrollThreshold) {
            // Scroll up
            container.scrollTop = Math.max(0, container.scrollTop - scrollSpeed);
        } else if (cursorY > rect.height - scrollThreshold) {
            // Scroll down
            container.scrollTop = Math.min(
                container.scrollHeight - container.clientHeight,
                container.scrollTop + scrollSpeed
            );
        }
    };

    // Track cursor position during drag for auto-scroll and drop position
    const handleDragMove = (e) => {
        if (isDragging) {
            handleAutoScroll(e);
            setCursorY(e.clientY);

            // Calculate drop position based on cursor Y position
            const container = scrollContainerRef.current;
            if (container) {
                const containerRect = container.getBoundingClientRect();
                const cursorY = e.clientY;

                // Find all widget elements
                const widgets = container.querySelectorAll('.p-card');
                let dropIndex = 0;

                // If no widgets, drop at position 0
                if (widgets.length === 0) {
                    setDropPosition(0);
                    return;
                }

                // Check each widget to find the correct drop position
                for (let i = 0; i < widgets.length; i++) {
                    const widget = widgets[i];
                    const widgetRect = widget.getBoundingClientRect();
                    const widgetMiddle = widgetRect.top + (widgetRect.height / 2);

                    if (cursorY < widgetMiddle) {
                        // Cursor is above the middle of this widget, drop before it
                        dropIndex = i;
                        break;
                    } else {
                        // Cursor is below the middle of this widget, drop after it
                        dropIndex = i + 1;
                    }
                }

                setDropPosition(dropIndex);
            }
        }
    };

    const itemDragStart = (e, item) => {
        console.log('Item drag start:', item);
        setDraggedItem(item);
        setDraggedElement(e.target);
        setIsDragging(true);
        e.dataTransfer.setData('text/plain', JSON.stringify(item));
        e.dataTransfer.effectAllowed = 'move';

        // Add visual feedback to the dragged element
        e.target.style.opacity = '0.5';
        e.target.style.transform = 'rotate(2deg)';
        e.target.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';

        // Force update to show drop indicators
        setTimeout(() => {
            forceUpdate();
        }, 10);
    };

    const widgetDragStart = (e, item) => {
        console.log('🚀 Widget drag start:', item);

        // Set global flag to prevent interference
        dragInProgress.current = true;

        // Set drag data first
        e.dataTransfer.setData('text/plain', JSON.stringify(item));
        e.dataTransfer.effectAllowed = 'copy';

        // Set states immediately
        setDraggedWidget(item);
        setDraggedElement(e.target);
        setDragOverIndex(null);
        setIsDragging(true);

        // Add visual feedback to the dragged widget
        e.target.style.opacity = '0.6';
        e.target.style.transform = 'scale(0.95)';
        e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';

        console.log('✅ Drag state set - isDragging: true, draggedWidget:', item);
        forceUpdate();
    };
    const getObjectAtIndex = (data, index) => {
        const keys = Object.keys(data);
        const key = keys[index];
        return data[key];
    };
    function validateArray(values) {
        let arr = JSON.parse(JSON.stringify(values))
        const uniqueValues = new Set();
        const invalidIndices = [];

        for (let i = 0; i < arr.length; i++) {
            const item = arr[i];
            item.value = item.value.toString()
            item.label = item.label.toString()

            if (
                typeof item.value === 'string' && item.value.trim() !== '' && // Value is not empty
                typeof item.label === 'string' && item.label.trim() !== '' && // Label is not empty
                !uniqueValues.has(item.value.trim()) // Value is unique
            ) {
                uniqueValues.add(item.value.trim());
            } else {
                invalidIndices.push(i);
            }
        }

        const isValid = invalidIndices.length === 0;

        return { result: isValid, index: invalidIndices };
    }
    const checkEmptyField = (obj) => {
        let flag = false

        if (obj.type === 5) {
            if (obj.data.label !== undefined && obj.data.label.trim().length !== 0) {
                flag = true
            }
        } else {
            if (obj.data.name !== undefined && obj.data.name.trim().length !== 0 && checkDPName(obj.data.name)) {
                if (obj.type === 3) {
                    console.log(obj.data.fraction)
                    if (obj.data.fraction !== undefined && obj.data.fraction !== null && obj.data.fraction >= 0) {
                        flag = true
                    } else {
                        flag = false
                    }
                } else {
                    flag = true
                }

            }
        }

        return flag
    }
    const saveCellConfig = () => {
        let loc = JSON.parse(JSON.stringify(form));

        let item = activecell
        // getObjectAtIndex(loc[activecellId.widgetind].data[activecellId.dataind], activecellId.colind)
        if (item.type === 4) {
            let validate = validateArray(item.data.values)
            console.log(validate, activecell.data.name)
            if (validate.result && activecell.data.name !== undefined && activecell.data.name.trim().length !== 0) {
                loc.data1[activecellId.widgetind].data[activecellId.dataind][loc.data1[activecellId.widgetind].value[activecellId.colind]] = activecell
                setForm(loc)
                setCellConfigDialog(false)
            }
        } else {
            if (checkEmptyField(activecell)) {

                loc.data1[activecellId.widgetind].data[activecellId.dataind][loc.data1[activecellId.widgetind].value[activecellId.colind]] = activecell
                setForm(loc)
                setCellConfigDialog(false)
            }
        }

        console.log(item)
    }
    const checkDPName = (str) => {
        let possibleIDs = ['SDP']
        return possibleIDs.some(prefix => str.trim().startsWith(prefix));
    }
    const cellEditFooter = () => {
        return (<>
            <Button
                label="Close"
                icon="pi pi-times"
                className="p-button-text"
                onClick={() => {
                    setCellConfigDialog(false);
                }}
            />
            <Button
                label="Save"
                icon="pi pi-check"
                className="p-button-text"
                onClick={() => {
                    saveCellConfig();
                }}
            />
        </>)
    }
    const itemDragEnd = (e) => {
        // Always reset styles regardless of drop success
        resetDragStyles();
    };

    const widgetDragEnd = (e) => {
        // Always reset styles regardless of drop success
        resetDragStyles();
    };
    const widgetDrop = (e) => {
        e.preventDefault();

        if (draggedWidget !== null) {
            form.data1.push(draggedWidget)
            forceUpdate()
        }

        // Always reset all styles after drop
        resetDragStyles();
    };

    const widgetDragLeave = (e) => {
        // Reset drop zone visual feedback when leaving
        if (e.currentTarget && !e.currentTarget.contains(e.relatedTarget)) {
            e.currentTarget.classList.remove('drop-zone-active');
            setDragOverIndex(null);
        }
    };

    // Global drag leave handler for document
    const handleGlobalDragLeave = (e) => {
        // If dragging outside the main container, reset styles
        if (!e.relatedTarget || !document.querySelector('.col-10').contains(e.relatedTarget)) {
            setDragOverIndex(null);
        }
    };
    const widgetDragOver = (ev) => {
        ev.preventDefault();
        ev.dataTransfer.dropEffect = 'copy';

        // Add visual feedback to drop zone
        if (ev.currentTarget && !ev.currentTarget.classList.contains('drop-zone-active')) {
            ev.currentTarget.classList.add('drop-zone-active');
        }
    };

    const itemDragOver = (ev) => {
        ev.preventDefault();
        ev.dataTransfer.dropEffect = 'move';
    };
    const itemDrop = (e, targetIndex) => {
        console.log('Drop at index:', targetIndex, 'draggedItem:', draggedItem, 'draggedWidget:', draggedWidget);
        e.preventDefault();
        e.stopPropagation();

        let success = false;

        // Handle existing widget reordering
        if (draggedItem !== null) {
            const fromIndex = form.data1.indexOf(draggedItem);
            if (fromIndex !== -1) {
                let loc = JSON.parse(JSON.stringify(form));
                const updatedItems = [...loc.data1];
                updatedItems.splice(fromIndex, 1);
                updatedItems.splice(targetIndex, 0, draggedItem);
                loc.data1 = updatedItems;
                setForm(loc);
                success = true;
                console.log('Moved existing widget from', fromIndex, 'to', targetIndex);
            }
        }

        // Handle new widget from panel
        if (draggedWidget !== null) {
            let loc = JSON.parse(JSON.stringify(form));

            // Create a new widget with unique ID
            const newWidget = {
                ...draggedWidget,
                id: Date.now() + Math.random() // Ensure unique ID
            };

            loc.data1.splice(targetIndex, 0, newWidget);
            setForm(loc);
            success = true;
            console.log('✅ Added new widget at index', targetIndex, 'Widget:', newWidget);
        }

        if (success) {
            console.log('✅ Drop successful!');
            // TODO: Add toast notification back later
            // toast.current.show({
            //     severity: 'success',
            //     summary: 'Widget Added',
            //     detail: `Widget added at position ${targetIndex + 1}`,
            //     life: 2000
            // });
        } else {
            console.log('❌ Drop failed - no item to drop');
        }

        // Always reset all styles after drop
        forceResetAfterDrop();
    };
    const validateID = () => {
        let loc = JSON.parse(JSON.stringify(form))
        let dps = []
        let allSDPTHIds = []
        let hasTableValidationError = false
        let detailedErrors = []
        let idTypeMap = new Map() // Track which type each ID belongs to

        loc.data1.forEach((item, itemIndex) => {
            console.log(item)
            if (item.type !== 'paragraph' && item.type !== 'htmleditor') {
                if (item.type !== 'table' && item.type !== 'tableadd') {
                    // Regular SDP IDs for non-table elements
                    if (item.name) {
                        const idName = item.name.trim().toLowerCase()
                        dps.push(idName)
                        idTypeMap.set(idName, { type: item.type.toUpperCase(), label: item.label || 'Unlabeled', index: itemIndex })
                    } else {
                        dps.push(null)
                    }

                } else {
                    // For table/tableadd elements, validate SDPTH IDs
                    const headerCount = item.value ? item.value.length : 0
                    const sdpthCount = item.headerIds ? item.headerIds.length : 0

                    console.log(`Table validation - Headers: ${headerCount}, SDPTH IDs: ${sdpthCount}`)

                    // Check that number of headers equals number of SDPTH IDs
                    if (headerCount !== sdpthCount) {
                        console.error(`Header count mismatch: ${headerCount} headers but ${sdpthCount} SDPTH IDs`)
                        hasTableValidationError = true
                        detailedErrors.push(`${item.type.toUpperCase()} "${item.label || 'Unlabeled'}": ${headerCount} headers but ${sdpthCount} SDPTH IDs`)
                    }

                    // Validate each SDPTH ID format
                    if (item.headerIds) {
                        item.headerIds.forEach((sdpthId, headerIndex) => {
                            if (sdpthId && sdpthId.trim() !== '') {
                                const idName = sdpthId.trim().toLowerCase()
                                if (idName.startsWith('sdpth')) {
                                    // Validate SDPTH format: SDPTH<form_id>_<number>
                                    const expectedPrefix = `sdpth${form.id}_`
                                    if (!idName.startsWith(expectedPrefix)) {
                                        console.error(`Invalid SDPTH format: ${sdpthId}`)
                                        hasTableValidationError = true
                                        detailedErrors.push(`${item.type.toUpperCase()} "${item.label || 'Unlabeled'}": Invalid SDPTH format "${sdpthId}"`)
                                    } else {
                                        allSDPTHIds.push(idName)
                                        const headerName = item.value[headerIndex] || `Header ${headerIndex + 1}`
                                        idTypeMap.set(idName, { type: `${item.type.toUpperCase()}-HEADER`, label: `${item.label || 'Unlabeled'} (${headerName})`, index: itemIndex })
                                    }
                                } else {
                                    console.error(`Invalid SDPTH ID: ${sdpthId}`)
                                    hasTableValidationError = true
                                    detailedErrors.push(`${item.type.toUpperCase()} "${item.label || 'Unlabeled'}": Invalid SDPTH ID "${sdpthId}"`)
                                }
                            }
                        })
                    }

                    // Also collect regular cell IDs for duplicate checking
                    Object.values(item.data).forEach((i) => {
                        Object.values(i).forEach((j) => {
                            if (j.type !== 5) {
                                const idName = j.data.name.trim().toLowerCase()
                                dps.push(idName)
                                idTypeMap.set(idName, { type: `${item.type.toUpperCase()}-CELL`, label: `${item.label || 'Unlabeled'} (Cell)`, index: itemIndex })
                            }
                        })
                    })
                }
            }
        })

        // Combine all IDs for duplicate checking
        const allIds = [...dps, ...allSDPTHIds]
        const { uniqueIds, duplicateIds } = checkNUDArray(allIds);

        // Create detailed duplicate information
        let duplicateDetails = []
        if (duplicateIds.length > 0) {
            duplicateIds.forEach(dupId => {
                const typeInfo = idTypeMap.get(dupId)
                if (typeInfo) {
                    duplicateDetails.push(`${dupId.toUpperCase()} (${typeInfo.type} - ${typeInfo.label})`)
                } else {
                    duplicateDetails.push(`${dupId.toUpperCase()} (Unknown type)`)
                }
            })
        }

        setDupIds(duplicateDetails) // Store detailed duplicate information
        console.log('Validation results:', { duplicateIds, duplicateDetails, uniqueIds, allIds, hasTableValidationError, detailedErrors })

        // Check that SDPTH IDs don't conflict with SDP IDs
        const sdpIds = dps.filter(id => id && id.startsWith('sdp'))
        const hasConflict = allSDPTHIds.some(sdpthId =>
            sdpIds.some(sdpId => sdpthId === sdpId)
        )

        if (hasConflict) {
            detailedErrors.push('SDPTH IDs conflict with SDP IDs')
        }

        // Store detailed errors for display
        if (detailedErrors.length > 0 || duplicateDetails.length > 0) {
            const allErrors = [...detailedErrors]
            if (duplicateDetails.length > 0) {
                allErrors.push(`Duplicate IDs found: ${duplicateDetails.join(', ')}`)
            }
            window.validationErrors = allErrors // Store for error display
        } else {
            window.validationErrors = []
        }

        return uniqueIds.length === allIds.length && !hasConflict && !hasTableValidationError
    }
    function checkNUDArray(array) {
        const uniqueIds = [];
        const duplicateIds = [];
        const seenItems = new Set();

        array.forEach((item) => {
            if (item !== null && item !== undefined) {
                if (typeof item === 'string') {
                    const trimmedItem = item.trim().toLowerCase();
                    if (trimmedItem !== '') {
                        if (!seenItems.has(trimmedItem)) {
                            uniqueIds.push(trimmedItem);
                            seenItems.add(trimmedItem);
                        } else if (!duplicateIds.includes(trimmedItem)) {
                            duplicateIds.push(trimmedItem);
                        }
                    }
                } else if (!seenItems.has(item.trim().toLowerCase())) {
                    uniqueIds.push(item.trim().toLowerCase());
                    seenItems.add(item.trim().toLowerCase());
                } else if (!duplicateIds.includes(item.trim().toLowerCase())) {
                    duplicateIds.push(item.trim().toLowerCase());
                }
            }
        });


        return { uniqueIds, duplicateIds };
    }
    const validateForm = () => {

        let loc = JSON.parse(JSON.stringify(form))
        let check = 0
        setSubmitted(true)
        loc.data1.forEach((item) => {
            if (item.type === 'text' || item.type === 'textarea' || item.type === 'number' || item.type === 'date' || item.type === 'file') {
                if (item.name === undefined || item.name.trim().length === 0 || !checkDPName(item.name) || item.label === undefined || item.label.trim().length === 0) {

                    check -= 1
                } else {
                    if (item.type === 'number') {
                        if (item.fraction !== null && item.fraction !== undefined && item.fraction >= 0) {
                            check += 1
                        } else {
                            check -= 1
                        }
                    } else {
                        check += 1
                    }

                }


            } else if (item.type === 'select' || item.type === 'radio-group' || item.type === 'checkbox-group') {
                if (item.name === undefined || item.name.trim().length === 0 || !checkDPName(item.name) || item.label === undefined || item.label.trim().length === 0) {
                    check -= 1

                } else if (item.values.length !== 0) {
                    validateArray(item.values).result === false ? check -= 1 : check += 1
                }
            } else if (item.type === 'checkpoint') {


                if (item.name === undefined || item.name.trim().length === 0 || !checkDPName(item.name) || item.label === undefined || item.label.trim().length === 0) {
                    check -= 1

                } else if (item.values !== undefined && item.values.length !== 0) {
                    item.values.filter((i) => { return i.label.toString().trim().length === 0 }).length === 0 ? check += 1 : check -= 1

                } else if (item.values === undefined || item.values.length === 0) {
                    check -= 1
                }

            } else if (item.type === 'paragraph' || item.type === 'htmleditor') {
                if (item.label === undefined || item.label.trim().length === 0) {

                    check -= 1
                } else {
                    check += 1
                }
            } else if (item.type === 'table') {


                if (item.label.trim().length !== 0 && item.value.length !== 0) {
                    if (item.data.length !== 0) {
                        let status = true
                        item.data.forEach((i) => {
                            item.value.forEach((j) => {
                                let cell = i[j]
                                if (cell.type === 4) {
                                    let validate = validateArray(cell.data.values)

                                    if (validate.result && cell.data.name !== undefined && cell.data.name.trim().length !== 0 && checkDPName(cell.data.name)) {


                                    } else {
                                        status = false
                                    }
                                } else {
                                    if (!checkEmptyField(cell)) {
                                        status = false

                                    }
                                }

                            })
                        })
                        console.log(status)
                        if (status) {
                            check += 1
                        } else {

                            check -= 1
                        }
                    } else {

                        check -= 1
                    }
                } else {

                    check -= 1
                }
            } else if (item.type === 'tableadd') {


                if (item.label.trim().length !== 0 && item.value.length !== 0 && item.maxrowlimit !== 0 && item.maxrowlimit <= 100) {
                    if (item.data.length === 1) {
                        let status = true
                        item.data.forEach((i) => {
                            item.value.forEach((j) => {
                                let cell = i[j]
                                if (cell.type === 4) {
                                    let validate = validateArray(cell.data.values)

                                    if (validate.result && cell.data.name !== undefined && cell.data.name.trim().length !== 0 && checkDPName(cell.data.name)) {


                                    } else {
                                        status = false
                                    }
                                } else {
                                    if (!checkEmptyField(cell)) {
                                        status = false

                                    }
                                }

                            })
                        })
                        console.log(status)
                        if (status) {
                            item.newrow = item.data
                            check += 1
                        } else {

                            check -= 1
                        }
                    } else {

                        check -= 1
                    }
                } else {

                    check -= 1
                }
            }
        })
        return check === loc.data1.length && loc.data1.length !== 0

    }
    const previewForm = () => {
        if (validateForm() && validateID()) {
            // const url = `/cf_preview_view/state=${encodeURIComponent(JSON.stringify(form))}`;
            // window.open(url, '_blank');

            navigate.push({ pathname: '/cf_preview_view', state: form })
        } else {
            // Show detailed validation errors
            let errorMessage = "Validation Errors:"
            if (window.validationErrors && window.validationErrors.length > 0) {
                errorMessage = window.validationErrors.join('\n• ')
                errorMessage = '• ' + errorMessage
            } else if (dupids && dupids.length > 0) {
                errorMessage = "Duplicate IDs found:\n• " + dupids.join('\n• ')
            } else {
                errorMessage = "Fill the mandatory fields"
            }

            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Validation Failed",
                text: errorMessage,
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
        }

    }
    const validateFrameworks = () => {
        let hasErrors = false;

        form.data1.forEach((widget) => {
            // Check Mandatory frameworks
            const mandatoryFrameworks = widget.framework || [];
            const mandatoryAssigned = widget.assignedFramework || {};
            mandatoryFrameworks.forEach(frameworkId => {
                const sections = mandatoryAssigned[frameworkId] || [];
                if (sections.length === 0) {
                    hasErrors = true;
                }
            });

            // Check Mandatory if Material frameworks
            const mandatoryIfMaterialFrameworks = widget.framework_mandatory_if_material || [];
            const mandatoryIfMaterialAssigned = widget.assignedFramework_mandatory_if_material || {};
            mandatoryIfMaterialFrameworks.forEach(frameworkId => {
                const sections = mandatoryIfMaterialAssigned[frameworkId] || [];
                if (sections.length === 0) {
                    hasErrors = true;
                }
            });
        });

        return !hasErrors;
    };

    const saveData = () => {
        console.log(validateForm(), validateID())
        if (validateForm() && validateID() && validateFrameworks() && form.title.trim().length !== 0 && form.data1.length !== 0 && form.formType) {
            let data = form;
            data.data1.forEach((item) => {
                if (item.type === 'tableadd') {
                    item['newrow'] = item.data
                }
            })
            delete data.data2
            delete data.type
            if (data.id === undefined) {
                data['curator_id'] = selector.id
                data['created'] = moment.utc()

                APIServices.post(API.SRF, data).then((res) => {
                    if (res.status === 200) {
                        setDupIds([])
                        setSubmitted(false)
                        setForm({ title: '', data1: [], comments: '', suffix: 'SRF' });
                        Swal.fire({
                            position: "center",
                            icon: "success",
                            title: "Consolidated Form Added Successfully",
                            showConfirmButton: false,
                            timer: 1500,
                        });
                        navigate.push({ pathname: '/form/cf_list' })
                        forceUpdate()
                    }
                })
            } else {

                delete data.created
                delete data.curator_id
                delete data.purpose
                delete data.suffix
                if (data.comments === null || data.comments === undefined) {
                    delete data.comments
                }
                data['modifier_id'] = selector.id
                data['updated'] = moment.utc()
                APIServices.patch(API.SRF_Edit(form.id), { ...data, data1: JSON.stringify(data.data1) }).then((res) => {
                    if (res.status === 204) {
                        setDupIds([])
                        setSubmitted(false)
                        setForm({ title: '', data1: [], comments: '', type: 1, suffix: 'SRF', formType: 1 });
                        Swal.fire({
                            position: "center",
                            icon: "success",
                            title: "Consolidated Form Updated Successfully",
                            showConfirmButton: false,
                            timer: 1500,
                        });
                        navigate.push({ pathname: '/form/cf_list' })
                        forceUpdate()
                    }
                })

            }


        } else {
            // Show detailed validation errors
            let errorMessage = "Validation Errors:"
            if (window.validationErrors && window.validationErrors.length > 0) {
                errorMessage = window.validationErrors.join('\n• ')
                errorMessage = '• ' + errorMessage
            } else if (dupids && dupids.length > 0) {
                errorMessage = "Duplicate IDs found:\n• " + dupids.join('\n• ')
            } else {
                errorMessage = "Fill the mandatory fields"
            }

            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Validation Failed",
                text: errorMessage,
                showConfirmButton: true,
                confirmButtonText: "OK"
            });
        }
    }
    const updateForm = (obj, value) => {
        let loc = JSON.parse(JSON.stringify(form))
        loc[obj] = value
        setForm(loc)
    }
    return (
        <>
            <style>
                {`
                    @keyframes pulse {
                        0% { opacity: 0.7; transform: scale(1); }
                        50% { opacity: 1; transform: scale(1.02); }
                        100% { opacity: 0.7; transform: scale(1); }
                    }
                `}
            </style>
            <div className=" card col-12" style={{ margin: 5, height: "calc(100vh - 9rem)", overflowY: 'scroll' }}>
            <div
                className="col-12"
                style={{
                    textDecoration: "underline",
                    textAlign: "center",
                    textDecorationColor: "green",
                }}
            >
                <h5> SRF {form.id} </h5>
            </div>
            <div
                className="col-12 "
                style={{
                    display: "flex",
                    flexDirection: "row",
                }}
            >
                <div
                    style={{
                        width: "20%",
                        display: "flex",
                        alignSelf: "center",
                    }}
                >
                    <span>
                        SRF Title
                        <span
                            style={{
                                color: "red",
                            }}
                        >
                            &nbsp;*
                        </span>
                    </span>
                </div>
                <div
                    style={{
                        width: "80%",
                    }}
                    className="p-inputgroup"
                >
                    <InputText
                        type={"text"}
                        className={submitted && form.title.trim().length === 0 && 'p-invalid'}
                        value={form.title}
                        onChange={(e) => {
                            updateForm('title', e.target.value)
                        }}
                        placeholder="SRF Title"
                    />
                </div>
            </div>
            <div
                className="col-12 "
                style={{
                    display: "flex",
                    flexDirection: "row",
                }}
            >
                <div
                    style={{
                        width: "20%",
                        display: "flex",
                        alignSelf: "center",
                    }}
                >
                    <span>
                        SRF Type
                        <span
                            style={{
                                color: "red",
                            }}
                        >
                            &nbsp;*
                        </span>
                    </span>
                </div>
                <div
                    style={{
                        width: "80%",
                    }}
                    className="p-inputgroup"
                >
                    <Dropdown

                        className={submitted && !(form.formType != null) && 'p-invalid'}
                        value={form.formType}
                        optionValue="id"
                        optionLabel="name"
                        options={[{ name: 'Supplier', id: 1 }, { name: 'Dealer', id: 2 }, { name: 'General', id: 3 }, { name: 'Self-Assessment', id: 4 }, { name: 'Other', id: 10 }]}
                        onChange={(e) => {
                            updateForm('formType', e.value)
                        }}
                        placeholder="Type"
                    />
                </div>
            </div>
            <div
                className="col-12 "
                style={{
                    display: "flex",
                    flexDirection: "row"
                }}
            >
                <div
                    style={{
                        width: "20%",
                        display: "flex",
                        alignSelf: "center",
                    }}
                >
                    <span>
                        Tags
                        <span
                            style={{
                                color: "red",
                            }}
                        >
                            &nbsp;*
                        </span>
                    </span>
                </div>
                <div
                    style={{
                        width: "80%",
                    }}
                    className="p-inputgroup"
                >
                    <MultiSelect value={!form.tags ? [] : form.tags.filter(i => clienttag.map(j => j.id).includes(i))} onChange={(e) => { setForm((prev) => ({ ...prev, tags: e.value })) }} style={{ width: '100%' }} options={clienttag} optionLabel="name" display='chip' optionValue="id"
                        placeholder="Select Tags" className="w-full" />

                </div>

            </div>
            <div
                className="col-12 "
                style={{
                    display: "flex",
                    flexDirection: "row",
                }}
            >
                <div
                    style={{
                        width: "20%",
                        display: "flex",
                        alignSelf: "center",
                    }}
                >
                    <span>
                        Description
                    </span>
                </div>
                <div
                    style={{
                        width: "80%",
                    }}
                    className="p-inputgroup"
                >
                    <InputTextarea
                        type={"text"}
                        value={form.comments}
                        onChange={(e) => {
                            updateForm('comments', e.target.value)
                        }}
                        placeholder="description"
                    />
                </div>
            </div>
            <div className='grid m-0 '>
                <div className="col-2 widget-panel"
                    onDragOver={(e) => {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = 'none';
                        if (isDragging) {
                            e.currentTarget.style.backgroundColor = '#ffebee';
                            e.currentTarget.style.border = '2px dashed #f44336';
                        }
                    }}
                    onDragLeave={(e) => {
                        if (isDragging) {
                            e.currentTarget.style.backgroundColor = '';
                            e.currentTarget.style.border = '';
                        }
                    }}
                    onDrop={(e) => {
                        e.preventDefault();
                        e.currentTarget.style.backgroundColor = '';
                        e.currentTarget.style.border = '';
                        // Don't allow dropping in widget panel
                        resetDragStyles();
                    }}>
                    <div className="widget-panel-title">
                        <i className="pi pi-th-large" style={{ marginRight: '8px' }}></i>
                        Widget Library
                    </div>
                    <div style={{
                        fontSize: '0.8rem',
                        color: '#6b7280',
                        marginBottom: '10px',
                        padding: '5px',
                        backgroundColor: '#f0f9ff',
                        borderRadius: '4px',
                        border: '1px solid #e0f2fe'
                    }}>
                        <i className="pi pi-info-circle" style={{ marginRight: '5px' }}></i>
                        Drag widgets to the form area or click to add
                    </div>

                    <div draggable
                        onDragStart={(e) => { widgetDragStart(e, getObjectByType('text')) }}
                        onDragEnd={() => { resetDragStyles() }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("text");
                        }}
                    >
                        <i className="pi pi-align-justify"></i> InputText {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'text').length})`) : null : null} </div>
                    <div draggable
                        onDragStart={(e) => { widgetDragStart(e, getObjectByType('number')) }}
                        onDragEnd={() => {
                            console.log('🏁 Drag ended for number widget');
                            setTimeout(() => resetDragStyles(), 100);
                        }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("number");
                        }}
                    >
                        <i style={{ fontSize: '1.1rem' }} className="material-icons">pin</i> Number {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'number').length})`) : null : null}
                    </div>
                    <div draggable
                        onDragStart={(e) => { widgetDragStart(e, getObjectByType('inputtextarea')) }}
                        onDragEnd={() => { resetDragStyles() }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("inputtextarea");
                        }}
                    >
                        <i style={{
                            fontSize: '12px',
                            paddingRight: '5px'
                        }}
                            className="pi
pi-pencil"
                        ></i> InputTextArea {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'textarea').length})`) : null : null}
                    </div>
                    <div draggable
                        onDragStart={(e) => { widgetDragStart(e, getObjectByType('paragraph')) }}
                        onDragEnd={() => { resetDragStyles() }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("paragraph");
                        }}
                    >
                        <i
                            className="pi pi-align-center"
                        ></i> Paragraph {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'paragraph').length})`) : null : null}

                    </div>
                    <div draggable
                        onDragStart={(e) => { widgetDragStart(e, getObjectByType('dropdown')) }}
                        onDragEnd={() => {
                            console.log('🏁 Drag ended for dropdown widget');
                            setTimeout(() => resetDragStyles(), 100);
                        }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("dropdown");
                        }}
                    >
                        <i className="pi pi-list"></i> Dropdown {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'select').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('table')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("table");
                        }}
                    >
                        <i className="pi pi-table"></i> Static Table {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'table').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('tableadd')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("tableadd");
                        }}
                    >
                        <i className="pi pi-table"></i> Dynamic Table {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'tableadd').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('checkpoint')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("checkpoint");
                        }}
                    >
                        <i className="pi pi-server"></i> Checkpoint {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'checkpoint').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('checkbox')) }}
                        onDragEnd={() => {
                            console.log('🏁 Drag ended for checkbox widget');
                            setTimeout(() => resetDragStyles(), 100);
                        }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("checkbox");
                        }}
                    >
                        <i className="pi pi-stop"></i> Checkbox {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'checkbox-group').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('radio')) }}
                        onDragEnd={() => {
                            console.log('🏁 Drag ended for radio widget');
                            setTimeout(() => resetDragStyles(), 100);
                        }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("radio");
                        }}
                    >
                        <i className="pi pi-stop-circle"></i> Radio {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'radio-group').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('date')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("date");
                        }}
                    >
                        <i className="pi pi-calendar"></i> Date {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'date').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('file')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("file");
                        }}
                    >
                        <i className="pi pi-file"></i> File Upload {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'file').length})`) : null : null}
                    </div>
                    <div draggable onDragStart={(e) => { widgetDragStart(e, getObjectByType('htmleditor')) }}
                        className="draggable-widget"
                        onClick={(e) => {
                            addWidget("htmleditor");
                        }}
                    >
                        <i style={{ fontSize: '1.1rem' }} className="material-icons">integration_instructions</i> HTML Editor {form ? form?.data1 ? (`(${form.data1.filter(i => i.type === 'htmleditor').length})`) : null : null}
                    </div>
                </div>
                <div
                    ref={scrollContainerRef}
                    className="col-10"
                    style={{
                        height: "55vh",
                        overflowY: "scroll",
                        marginTop: 10,
                        transition: 'all 0.3s ease',
                        borderRadius: '8px',
                        minHeight: '200px',
                        position: 'relative',
                        border: isDragging ? '3px dashed #315975' : '1px solid #e0e0e0',
                        backgroundColor: isDragging ? '#f0f4f7' : 'transparent'
                    }}
                    onDragOver={(e) => {
                        e.preventDefault();
                        handleDragMove(e);
                    }}
                    onDragEnter={(e) => {
                        e.preventDefault();
                        handleDragMove(e);
                    }}
                >





                    {form.data1.length === 0 && !isDragging && (
                        <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '300px',
                            color: '#6b7280',
                            textAlign: 'center'
                        }}>
                            <i className="pi pi-plus-circle" style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}></i>
                            <h3 style={{ margin: '0 0 0.5rem 0', fontWeight: 500 }}>Start Building Your Form</h3>
                            <p style={{ margin: 0, opacity: 0.7 }}>Drag widgets from the left panel or click to add them</p>
                        </div>
                    )}

                    {form.data1.map((widget, index) => (
                        <div key={index}>
                            {/* Show drop indicator before this widget if it's the drop position */}
                            {isDragging && dropPosition === index && (
                                <div
                                    style={{
                                        height: '50px',
                                        backgroundColor: '#f0f4f7',
                                        margin: '10px 0',
                                        borderRadius: '8px',
                                        border: '2px dashed #315975',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        cursor: 'pointer',
                                        fontSize: '14px',
                                        fontWeight: 'bold',
                                        color: '#315975',
                                        animation: 'pulse 1s infinite'
                                    }}
                                    onDrop={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        itemDrop(e, index);
                                    }}
                                    onDragOver={(e) => {
                                        e.preventDefault();
                                        e.dataTransfer.dropEffect = 'copy';
                                    }}
                                >
                                    📍 Drop Here
                                </div>
                            )}
                            {renderComponent(widget, index)}
                        </div>
                    ))}

                    {/* Show drop indicator at the end if that's the drop position */}
                    {isDragging && dropPosition === form.data1.length && (
                        <div
                            style={{
                                height: '50px',
                                backgroundColor: '#f0f4f7',
                                margin: '10px 0',
                                borderRadius: '8px',
                                border: '2px dashed #315975',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: 'bold',
                                color: '#315975',
                                animation: 'pulse 1s infinite'
                            }}
                            onDrop={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                itemDrop(e, form.data1.length);
                            }}
                            onDragOver={(e) => {
                                e.preventDefault();
                                e.dataTransfer.dropEffect = 'copy';
                            }}
                        >
                            📍 Drop Here
                        </div>
                    )}
                </div>
            </div>
            <div className="col-12" style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button style={{ marginRight: 10 }} rounded text raised aria-label="Filter" onClick={() => { let loc = JSON.parse(JSON.stringify(form)); loc.data1 = []; setForm(loc) }} >Clear </Button>

                {params.state !== null && params.state !== undefined && <Button style={{ marginRight: 10 }} rounded text raised aria-label="Filter" onClick={() => { navigate.goBack() }}>Back</Button>}
                <Button style={{ marginRight: 10 }} rounded text raised aria-label="Filter" onClick={() => { saveData() }}>Save </Button>

                {/* <Button icon='pi pi-cloud-download' rounded text raised aria-label="Filter" onClick={() => { exportTable2Excel(0) }}> </Button> */}
            </div>

            <Dialog
                visible={cellconfigdialog}
                style={{
                    width: "65%"
                }}
                header="Edit Cell Configuration"
                modal
                className="p-fluid"
                footer={cellEditFooter}
                onHide={() => { setCellConfigDialog(false) }}
            >
                <div>
                    <Dropdown value={activecell.type} onChange={(e) => { let loc = activecell; loc.type = e.value; if (e.value === 4) { loc.data.values = [{ label: '', value: '1' }, { label: '', value: '2' }] }; setActiveCell(() => loc); forceUpdate(); }} optionLabel="name" optionValue="id" options={[{ name: 'Text', id: 1 }, { name: 'TextArea', id: 2 }, { name: 'Number', id: 3 }, { name: 'Dropdown', id: 4 }, { name: 'Date', id: 6 }, { name: 'Label', id: 5 }]} />

                    {activecell.type !== 5 && activecell.data !== undefined &&
                        <div className="flex align-items-center" style={{ margin: 10 }}>
                            <Checkbox
                                inputId={"ac"}
                                value={activecell.data.required === undefined ? false : activecell.data.required}
                                onChange={(e) => {
                                    activecell.data['required'] = !e.value;
                                    forceUpdate();
                                }}
                                checked={activecell.data.required === undefined ? false : activecell.data.required}
                            />
                            <label htmlFor={"ac"} className="ml-2">
                                is Required
                            </label>
                        </div>

                    }
                    {activecell.type !== 5 &&
                        <>
                            <div className="m-2">
                                <label htmlFor="id" className="font-bold block mb-2">ID</label>
                                <InputText type="text" className={((activecell.data.name === undefined || activecell.data.name.trim().length === 0) || dupids.includes(activecell.data.name.trim().toLowerCase()) || !checkDPName(activecell.data.name)) && 'p-invalid'} value={activecell.data.name === undefined ? '' : activecell.data.name} onChange={(e) => { activecell.data.name = e.target.value.trim(); forceUpdate(); }} placeholder="ID" />
                            </div>
                            {activecell.data.name !== undefined && (dupids.includes(activecell.data.name.trim().toLowerCase())) && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Duplicate ID Found</small>
                            }
                        </>
                    }
                    {activecell.type === 3 &&
                        <>
                            <div className="m-2">
                                <label htmlFor="id" className="font-bold block mb-2">Fraction</label>
                                <InputNumber min={0} className={((activecell.data.fraction === undefined || activecell.data.fraction < 0)) && 'p-invalid'} value={activecell.data.fraction === undefined ? null : activecell.data.fraction} onChange={(e) => { activecell.data.fraction = e.value; forceUpdate(); }} placeholder="Fraction" />
                            </div>
                            {activecell.data.fraction !== undefined && submitted &&
                                <small style={{
                                    margin: '-10px 5px 0px 10px',
                                    color: 'red'
                                }}>Fraction Value should be atleast 0</small>
                            }
                            <div className="m-2">
                                <label htmlFor="id" className="font-bold block mb-2">Does a higher value represent improvement, or does a lower value represent improvement? (0-Higher,1-Lower)</label>
                                <InputNumber min={0} max={1} value={activecell.data.directionsign === undefined ? null : activecell.data.directionsign} onChange={(e) => { activecell.data.directionsign = e.value; forceUpdate(); }} placeholder="0/1" />
                            </div>
                        </>
                    }
                    {activecell.type === 5 &&
                        <div className="m-2">
                            <label htmlFor="phs" className="font-bold block mb-2">Label</label>
                            <InputText type="text" className={(activecell.data.label === undefined || activecell.data.label.trim().length === 0) && 'p-invalid'} value={activecell.data.label === undefined ? '' : activecell.data.label} onChange={(e) => { activecell.data.label = e.target.value; forceUpdate(); }} placeholder="label Value" />
                        </div>
                    }
                    {activecell.type < 5 &&
                        <div className="m-3">
                            <label htmlFor="pha" className="font-bold block mb-2">Placeholder</label>
                            <InputText type="text" value={activecell.data.placeholder === undefined ? '' : activecell.data.placeholder} onChange={(e) => { activecell.data.placeholder = e.target.value; forceUpdate(); }} placeholder="Placeholder" />
                        </div>
                    }
                    {activecell.type === 4 &&
                        <div className="m-3">
                            <label htmlFor="ph" className="font-bold block mb-2">Options</label>
                            <div style={{ maxHeight: 200, overflowY: 'scroll' }} >
                                {activecell.data.values.map((i, index) => {
                                    console.log(index, activecell.data.values.length)
                                    return (
                                        <div className="col-12" style={{ padding: 5 }}>
                                            <div className="grid" style={{ alignItems: 'center' }}>
                                                <div className="col-4" >

                                                    <InputText type="text" value={i.label} className={(i.label.toString().trim().length === 0) && 'p-invalid'} onChange={(e) => { activecell.data.values[index].label = e.target.value; forceUpdate(); }} placeholder="label" />
                                                </div>

                                                <div className="col-4">

                                                    <InputText type="text" className={(i.value.toString().trim().length === 0 || !isValueUniqueInArray(i.value, activecell.data.values, 'value')) && 'p-invalid'} value={i.value} onChange={(e) => { activecell.data.values[index].value = e.target.value; forceUpdate(); }} placeholder="value" />


                                                </div>

                                                <div className="col-1">
                                                    {((activecell.data.values.length - 1 === index) || (index === 1 && activecell.data.values.length === 2)) ?
                                                        <div>
                                                            <i className="material-icons" onClick={(e) => { e.stopPropagation(); addNewRow(activecell.data.values) }} style={{ color: 'green' }}>add_box</i>
                                                            {index > 1 && <i className="material-icons" onClick={() => { deleteRow(index) }}>delete</i>}

                                                        </div> :
                                                        index > 1 &&
                                                        <div>
                                                            <i className="material-icons" onClick={() => { deleteRow(index) }}>delete</i>

                                                        </div>

                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>

                        </div>
                    }
                </div>
            </Dialog>


        </div>
        </>
    );
};
const FrameworkComponent = ({ form, widget, frameworkList, forceUpdate, index }) => {
    console.log(widget)

    // Tag categories with their corresponding keys
    const tagCategories = [
        {
            id: 'mandatory',
            title: 'Mandatory',
            frameworkKey: 'framework', // Use existing key for backward compatibility
            assignedKey: 'assignedFramework' // Use existing key for backward compatibility
        },
        {
            id: 'mandatory_if_material',
            title: 'Mandatory if Material',
            frameworkKey: 'framework_mandatory_if_material',
            assignedKey: 'assignedFramework_mandatory_if_material'
        },
        {
            id: 'good_to_have',
            title: 'Good to Have',
            frameworkKey: 'framework_good_to_have',
            assignedKey: 'assignedFramework_good_to_have'
        },
        {
            id: 'not_required',
            title: 'Not Required',
            frameworkKey: 'framework_not_required',
            assignedKey: 'assignedFramework_not_required'
        }
    ];

    return (
        <>
            {form?.data1?.[index] && <div className="col-12">
                <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                    Framework Selection
                </label>

                {/* Show available frameworks */}
                <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
                    <label style={{ fontWeight: 'bold', marginBottom: '5px', display: 'block', fontSize: '14px' }}>
                        Not Mapped Yet:
                    </label>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                        {(() => {
                            const allSelected = [
                                ...(form.data1[index]?.framework || []),
                                ...(form.data1[index]?.framework_mandatory_if_material || []),
                                ...(form.data1[index]?.framework_good_to_have || []),
                                ...(form.data1[index]?.framework_not_required || [])
                            ];
                            const available = frameworkList.filter(f => !allSelected.includes(f.id));
                            return available.length > 0
                                ? available.map(f => f.title).join(', ')
                                : 'All frameworks are mapped';
                        })()}
                    </div>
                </div>

                {tagCategories.map((category) => {
                    const rawSelectedFrameworks = form.data1[index]?.[category.frameworkKey] || [];

                    // Get available options (exclude frameworks selected in other categories)
                    const getAvailableOptions = () => {
                        if (category.id === 'mandatory') {
                            // Mandatory can't select frameworks from Mandatory if Material
                            const excludeIds = form.data1[index]?.framework_mandatory_if_material || [];
                            return frameworkList.filter(f => !excludeIds.includes(f.id));
                        } else if (category.id === 'mandatory_if_material') {
                            // Mandatory if Material can't select frameworks from Mandatory
                            const excludeIds = form.data1[index]?.framework || [];
                            return frameworkList.filter(f => !excludeIds.includes(f.id));
                        } else if (category.id === 'good_to_have') {
                            // Good to Have can't select frameworks from mandatory categories or Not Required
                            const excludeIds = [
                                ...(form.data1[index]?.framework || []),
                                ...(form.data1[index]?.framework_mandatory_if_material || []),
                                ...(form.data1[index]?.framework_not_required || [])
                            ];
                            const availableFrameworks = frameworkList.filter(f => !excludeIds.includes(f.id));
                            // If no frameworks available, return empty array to show null options
                            return availableFrameworks.length > 0 ? availableFrameworks : [];
                        } else if (category.id === 'not_required') {
                            // Not Required can't select frameworks from mandatory categories or Good to Have
                            const excludeIds = [
                                ...(form.data1[index]?.framework || []),
                                ...(form.data1[index]?.framework_mandatory_if_material || []),
                                ...(form.data1[index]?.framework_good_to_have || [])
                            ];
                            const availableFrameworks = frameworkList.filter(f => !excludeIds.includes(f.id));
                            // If no frameworks available, return empty array to show null options
                            return availableFrameworks.length > 0 ? availableFrameworks : [];
                        }
                        return frameworkList;
                    };

                    const selectedFrameworks = rawSelectedFrameworks.filter(x => getAvailableOptions().map(f => f.id).includes(x)) || [];
                    const showAccordion = category.id === 'mandatory' || category.id === 'mandatory_if_material';

                    return (
                        <div key={category.id} style={{ marginBottom: '20px' }}>
                            <label style={{ fontWeight: 'bold', marginBottom: '5px', display: 'block' }}>
                                {category.title}
                            </label>

                            <MultiSelect
                                value={selectedFrameworks}
                                  panelClassName="hidefilter"
                                  filter
                                options={getAvailableOptions()}
                                onChange={(e) => {
                                    form.data1[index][category.frameworkKey] = e.value;

                                    // Clean up assignedFramework when frameworks are deselected
                                    if (form?.data1[index]?.[category.assignedKey]) {
                                        form.data1[index][category.assignedKey] = Object.fromEntries(
                                            Object.entries(form.data1[index][category.assignedKey]).filter(([key]) => e.value.includes(Number(key)))
                                        );
                                    }
                                    forceUpdate();
                                }}
                                optionLabel="title"
                                optionValue="id"
                                placeholder={`Select Frameworks for ${category.title}`}
                                style={{ width: "100%", marginBottom: "10px" }}
                            />

                            {showAccordion && selectedFrameworks.length > 0 && (
                                <Accordion style={{ marginTop: "10px" }}>
                                    <AccordionTab header={`Map sections for selected frameworks - ${category.title}`}>
                                        <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
                                            {selectedFrameworks.map((frameworkId) => {
                                                const framework = frameworkList.find((f) => f.id === frameworkId);
                                                const sections = form.data1[index]?.[category.assignedKey]?.[frameworkId] || [];
                                                const hasValidationError = sections.length === 0;

                                                return (
                                                    <div key={frameworkId} style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                                                        <span style={{ fontWeight: "bold" }}>{framework?.title}</span>
                                                        <Chips
                                                            style={{
                                                                width: '100%',
                                                                display: 'block'
                                                            }}
                                                            value={sections}
                                                            onChange={(e) => {
                                                                if (form?.data1[index]?.[category.assignedKey]?.[frameworkId]) {
                                                                    form.data1[index][category.assignedKey][frameworkId] = e.value;
                                                                } else {
                                                                    let obj = form.data1[index][category.assignedKey] || {};
                                                                    form.data1[index][category.assignedKey] = { ...obj, [frameworkId]: e.value };
                                                                }
                                                                forceUpdate();
                                                            }}
                                                            separator=","
                                                            placeholder={`Add sections for ${framework?.title}`}
                                                        />
                                                        {hasValidationError && (
                                                            <small
                                                                className="p-invalid"
                                                                style={{
                                                                    color: "red",
                                                                    marginTop: '2px'
                                                                }}
                                                            >
                                                                ⚠️ {category.title}: {framework?.title} requires sections
                                                            </small>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </AccordionTab>
                                </Accordion>
                            )}
                        </div>
                    );
                })}

                {(form.data1[index]?.framework?.length > 0 || form.data1[index]?.framework_mandatory_if_material?.length > 0) && (
                    <small
                        className="p-invalid"
                        style={{
                            color: "gray", marginLeft: '3%'
                        }}
                    >
                        ( press , or enter key to add sections )
                    </small>
                )}
            </div>}
        </>
    );
};
const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(ConsolidateForm, comparisonFn);
