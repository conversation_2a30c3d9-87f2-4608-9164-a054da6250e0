.drag-container {
    display: flex;
    gap: 10px;
}

.drag-item {
    width: 100px;
    height: 100px;
    background-color: lightblue;
    text-align: center;
    line-height: 100px;
    cursor: pointer;
}

.react-datepicker-wrapper {
    display: flex;
}

.react-datepicker-wrapper .react-datepicker__input-container {
    display: flex;
    width: 100%;
}

.react-datepicker__input-container input {
    height: 40px;
    width: 100%;
    background: #ffffff;
    border: 1px solid #ced4da;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
}

.react-datepicker__header {
    background: white;
}

.p-dropdown-items-wrapper {
    height: auto;
}

/* Enhanced Draggable Widget Styles */
.draggable-widget {
    margin: 5px;
    padding: 10px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #374151;
    user-select: none;
}

.draggable-widget:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.draggable-widget:active {
    cursor: grabbing;
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
}

.draggable-widget i {
    color: #6b7280;
    transition: color 0.3s ease;
}

.draggable-widget:hover i {
    color: #3b82f6;
}

/* Drop zone styles */
.drop-zone-active {
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    border-radius: 8px;
    animation: pulse-border 1.5s infinite;
}

@keyframes pulse-border {
    0% {
        border-color: #2196f3;
        background-color: #e3f2fd;
    }
    50% {
        border-color: #1976d2;
        background-color: #bbdefb;
    }
    100% {
        border-color: #2196f3;
        background-color: #e3f2fd;
    }
}

.drop-indicator {
    position: relative;
    cursor: pointer;
}

.drop-indicator::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #2196f3, transparent);
    transform: translateY(-50%);
    opacity: 0.5;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background: linear-gradient(90deg, transparent, #2196f3, transparent);
    }
    50% {
        background: linear-gradient(90deg, transparent, #1976d2, transparent);
    }
    100% {
        background: linear-gradient(90deg, transparent, #2196f3, transparent);
    }
}

/* Enhanced drop zone feedback */
.drop-zone-active {
    animation: pulse-border 1.5s infinite, glow 2s infinite alternate !important;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(33, 150, 243, 0.8);
    }
}

/* Drop indicator zones */
.drop-indicator-zone {
    transition: all 0.3s ease !important;
    border: 2px dashed rgba(33, 150, 243, 0.3) !important;
}

.drop-indicator-zone:hover {
    border-color: #2196f3 !important;
    background-color: #e3f2fd !important;
    transform: scale(1.02);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7);
    }
    70% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

/* Widget panel styling */
.widget-panel {
    background: #f8fafc;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #e5e7eb;
}

.widget-panel-title {
    font-weight: 600;
    color: #374151;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #e5e7eb;
}

.p-tabmenu .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {
    background: #ffffff;
    border-color: #315975;
    color: #315975;
    padding: 5px;
}

.p-menuitem-text {
    font-size: 13px;
}

.p-ink {
    background-color: unset;
}

.user-data-table-height .p-datatable-wrapper {
    height: 570px;
}

.auto-data-table-height .p-datatable-wrapper {
    height: auto;
    max-height: 300px;
    overflow: auto;
}

.p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link:not(.p-disabled):focus {
    box-shadow: none;
}

.br-5 {
    border-radius: 5px;
}

.br-1 {
    border-radius: 10px;
}

.br-2 {
    border-radius: 20px;
}

.layout-topbar .custom-layout-topbar-menu {
    margin-left: 0;
    position: absolute;
    flex-direction: column;
    background-color: var(--surface-overlay);
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05), 0px 1px 4px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 1rem;
    right: 2rem;
    top: 5rem;
    min-width: 15rem;
    display: none;
    animation: scalein 0.15s linear;
}

.d-none {
    display: none !important;
}

.layout-topbar .layout-topbar-button span {
    font-size: 1rem;
    display: block;
}

.p-tabmenu .p-tabmenu-nav .p-tabmenuitem {
    margin-right: 0;
    justify-content: center;
    display: flex;
}

.p-checkbox .p-checkbox-box.p-highlight {
    border-color: #315975;
    background: #315975;
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    border-color: #315975;
    background: #315975;
    color: #ffffff;
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #315975;
}

.p-radiobutton .p-radiobutton-box.p-highlight {
    border-color: #315975;
    background: #315975;
}

.p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
    border-color: #315975;
}

.p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    border-color: #315975;
    background: #315975;
    color: #ffffff;
}

.p-radiobutton .p-radiobutton-box:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}

.p-dropdown:not(.p-disabled):hover {
    border-color: #315975;
}

.p-dropdown:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}

.p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}

.p-inputtext:enabled:hover {
    border-color: #315975;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}

.p-button:focus {
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(49, 89, 117, .06274509803921569), 0 1px 2px 0 #000;
    /* box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #31597510, 0 1px 2px 0 black; */
}

.p-button.p-button-text:enabled:hover,
.p-button.p-button-text:not(button):not(a):not(.p-disabled):hover {
    background: #31597510;
    color: #315975;
    border-color: transparent;
}

.p-button.p-button-outlined:enabled:hover,
.p-button.p-button-outlined:not(button):not(a):not(.p-disabled):hover {
    background: #31597510;
    color: #315975;
    border: 1px solid;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    color: #315975;
    background: #31597510;
}

.padding-8 {
    padding: 8px;
}

.p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #315975;
}

.p-inputswitch.p-focus .p-inputswitch-slider {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none
}

.p-inputswitch.p-inputswitch-checked:not(.p-disabled):hover .p-inputswitch-slider {
    background: #315975;
}

.h-500 .p-datatable-wrapper {
    height: 500px;
}

.h-max .p-datatable-wrapper {
    height: calc(100vh - 350px);
}

.user-ass-tab-1 .p-datatable-wrapper {
    height: calc(100vh - 300px);
}

.user-ass-tab-2 .p-datatable-wrapper {
    height: calc(100vh - 360px);
}

.user-ass-tab-3 .p-datatable-wrapper {
    height: calc(100vh - 360px);
}

.ql-tvs-approval .p-datatable-wrapper {
    height: calc(100vh - 310px);
}

.user-ass-tab-4 .p-datatable-wrapper {
    height: calc(100vh - 300px);
}

.user-ass-tab-5 .p-datatable-wrapper {
    height: calc(100vh - 380px);
}

.inactive-btn {
    width: 200px;
    background: lightgray !important;
    border: 0px;

}

.active-btn {
    width: 200px;
    border: 0px;

}

.text-underline {
    text-decoration: underline;
}

.dis-option-hover:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled):hover {
    background: none;
    cursor: default;
}

.dis-option-hover .p-listbox-list .p-listbox-item:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: #ffffff;
    border-color: #12344b;
    color: #12344b;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none
}

.p-tabview .p-tabview-nav .p-tabview-ink-bar {
    z-index: 1;
    display: block;
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #12344b;
    transition: 500ms cubic-bezier(0.35, 0, 0.25, 1);
}

.p-checkbox-checked.p-checkbox-disabled .p-checkbox-box {
    border: gray !important;
    background: gray !important;
}

.text-three-dot {

    text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
    white-space: nowrap;

}

.p-multiselect.p-multiselect-chip .p-multiselect-token {
    background: #31597520;
    color: #315975;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
}

.row {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 0;
    display: flex;
    flex-wrap: wrap;
    margin-top: calc(-1 * var(--bs-gutter-y));
    margin-right: calc(-.5 * var(--bs-gutter-x));
    margin-left: calc(-.5 * var(--bs-gutter-x));
}

.p-component {
    font-family: 'Lato', sans-serif !important;
}

.p-inputtext {
    font-family: 'Lato', sans-serif !important;
}

.tag-tooltip .p-tooltip-text {
    background: white;
}

.p-dropdown.p-disabled .p-dropdown-trigger svg {
    display: none;
}

.p-button:disabled {
    background: #80808050 !important;
    border: 1px solid #80808080 !important;
    color: #808080 !important
}

.text-area .ql-toolbar .ql-formats .ql-image {
    display: none !important;
}

.html-editor .ql-toolbar .ql-formats .ql-header,
.html-editor .ql-toolbar .ql-formats .ql-font,
.html-editor .ql-toolbar .ql-formats .ql-italic,
.html-editor .ql-toolbar .ql-formats .ql-color-picker,
.html-editor .ql-toolbar .ql-formats .ql-image {
    display: none !important;
}

.p-progressbar .p-progressbar-label {
    color: white;
    font-weight: 500;
    line-height: 1.5rem;
}

.p-progressbar .p-progressbar-value {
    border: 0 none;
    margin: 0;
    background: #315975;
}

.p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: none;
}

textarea {
    resize: none;
}

.accfull a .p-accordion-header-text {
    width: 100%;
}

.roundedge ul li.p-highlight {
    border: 2px solid #315975;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    overflow: hidden;
}

.p-column-filter-menu-button.p-column-filter-menu-button-active,
.p-column-filter-menu-button.p-column-filter-menu-button-active:hover {
    background: rgb(18, 52, 75, 0.8) !important;
    color: #f2f2f2 !important;
}

.table-link-clickable {
    cursor: pointer;
    color: #315975;
    font-size: 14px;
    font-weight: 600;
    text-decoration: underline;
}

.p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 1rem;
    border: 1px solid #dee2e6;
    color: #6c757d;
    background: #f8f9fa;
    font-weight: 700;
    border-radius: 6px;
    transition: box-shadow 0.2s;
}

.p-datatable .p-datatable-loading-icon {
    width: 3rem;
    height: 3rem;
}
