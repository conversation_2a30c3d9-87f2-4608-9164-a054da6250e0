import React, { useEffect, useState, useRef } from "react";
import APIServices from "../../../../service/APIService";
import { API } from "../../../../constants/api_url";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Button } from "primereact/button";
import { MultiSelect } from "primereact/multiselect";
import { Dialog } from "primereact/dialog";
import { Checkbox } from 'primereact/checkbox';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputNumber } from 'primereact/inputnumber';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Tooltip } from 'primereact/tooltip';
import { Editor } from 'primereact/editor';
import { Panel } from 'primereact/panel';
import { Card } from 'primereact/card';
import { saveAs } from "file-saver";
import { DateTime } from "luxon";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import Swal from "sweetalert2";
import 'primeflex/primeflex.css';
import { TabMenu } from "primereact/tabmenu";
import { Tab, Row, Col, Nav, Table, Form, Modal } from 'react-bootstrap';
import SupplierPanel from "../SupplierScreen/SupplierPanel";
import { SupplierMSIDashboard } from "./SupplierMSIDashboard";
import { getMonthsBetweenNew, getRPTextFormat, groupArrayByKeys } from "../../../../components/BGHF/helper";
import { Badge } from "primereact/badge";
import { Tag } from "primereact/tag";
import { Accordion, AccordionTab } from "primereact/accordion";
import SupplierReport from "./SupplierReport";
import AuditPanel from "../Auditor/AuditPanel";
import TakeActionSupplier from "./TakeActionSupplier";
import TakeActionDealer from "./TakeActionDealer";
import DealerActionHistoryDialog from "../MSI/DealerActionHistoryDialog";
import SupplierActionPlanDialog from "../MSI/SupplierActionPlanDialog";
import DealersSelfAssessmentTable from "../MSI/DealersSelfAssessmentTable";
import DealersTableCompleted from "../MSI/DealersTableCompleted";
import moment from "moment";

const SupplierHomeTVS = () => {
    const [dealerData, setDealerData] = useState([]);
    const [assignments, setAssignments] = useState([])
    const [data, setData] = useState([])
    const [submissiondata, setSubmissionData] = useState([])
    const [submittedAssessment, setSubmittedAssessment] = useState([])
    const [actionlist, setActionList] = useState([])
    const [activeAss, setActiveAss] = useState(null)
    const [asssrflist, setAssSrfList] = useState([])
    const [activeindex, setActiveIndex] = useState(0)
    const [activeindex2, setActiveIndex2] = useState('My Actions')
    const [reportdialog, setReportDialog] = useState(false)
    const [showSupplierActionPlanDialog, setShowSupplierActionPlanDialog] = useState(false)
    const [load, setLoad] = useState(true)

    const [activeform, setActiveForm] = useState({})
    const [selectedGmsOne, setSelectedGmsOne] = useState('GMS1'); // Default to GMS1
    const [currentFormData, setCurrentFormData] = useState({
        status: null,
        targetDate: null
    });
    const [takeActionData, setTakeActionData] = useState([])
    const [takeActionModal, setTakeActionModal] = useState(false)
    const [showDealerActionHistoryDialog, setShowDealerActionHistoryDialog] = useState(false)
    const [dealerActionHistoryData, setDealerActionHistoryData] = useState([])
    const [supplierActionPlanData, setSupplierActionPlanData] = useState({})
    const [takeDealerModal, setTakeDealerModal] = useState(false)
    const [takeActionDealer, setTakeActionDealer] = useState([]);

    const dealerList = useSelector(state => state.userlist.dealerList)
    const tvsExtUserList = useSelector(state => state.userlist.tvsExtUserList)
    const [globalFilter, setGlobalFilter] = useState('');

    // Date filter state for submitted_on
    const [dateFilter, setDateFilter] = useState({
        start: null,
        end: null
    });

    // Filtered data state
    const [filteredData, setFilteredData] = useState([]);

    // DataTable filter state to preserve filters when dialogs open/close
    const [dataTableFilters, setDataTableFilters] = useState({
        timeLine: { value: null, matchMode: "in" },
        title: { value: null, matchMode: "in" },
        currentStatus: { value: null, matchMode: "in" },
        submitted_on: { value: null, matchMode: "in" },
        formType: { value: null, matchMode: "in" },
    });

    // Function to handle modal close without data refresh
    const handleModalClose = (modalSetter, shouldRefresh = false) => {
        modalSetter(false);
        if (shouldRefresh) {
            updateActions();
            forceUpdate();
        }
    };

    const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }, { name: 'IDM (Indirect Material)', value: 10 }, { name: 'Import', value: 11 }]
    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]
    const vendorCode = useSelector((state) => state.user.currentVendorCode);
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const [dealerassessorlist, setDealerAssessorList] = useState([])

    useEffect(() => {
        if (vendorCode?.code && dealerData.length === 0) {
            refreshDealerData()
        }
    }, [vendorCode?.code])


    const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]

    const getCalibirationId = (rowData) => {
        return 'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')
    }
    const refreshDealerData = async () => {
        let dealerAssUri = {
            "include": ['dealer', 'dealerAuditorChecklistSubmission', 'vendor', 'actions']
        }

        const promise0 = []
        const promise1 = []
        const promise2 = APIServices.get(API.GetRole_Up(admin_data.id))
        const promise3 = APIServices.get(API.DealerAssessmentAss_Up(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(dealerAssUri))}`)


        Promise.all([promise0, promise1, promise2, promise3]).then((values) => {
            for (const item of values[3].data) {
                item.cat = dealerType.find(i => i.value === item?.vendor?.dealerCategory)?.name || 'Not Found'
                item.msiId = getCalibirationId(item)
                item.dealerName = item?.vendor?.dealerName || 'NA'
                item.location = item?.vendor?.dealerLocation || 'NA'
                item.zone = zonalOfficeList.find(i => i.value === item?.vendor?.dealerZone)?.name || 'Not Found'
            }

            setDealerData(values[3].data.filter(x => x.vendorCode === vendorCode?.code))

            setDealerAssessorList([...tvsExtUserList.filter(x => !x.blocked), ...userList].filter(i => values[2].data.some(x => x.user_id === i.id && x.roles && x.roles.includes(19))))

        })
    }

    // State for GMS Two nested tabs
    const [selectedGmsTwo, setSelectedGmsTwo] = useState('2.1'); // Default to 2.1

    // Read-only flag
    const [readOnly, setReadOnly] = useState(false); // Change to true if necessary

    const [show, setShow] = useState(false)
    const userList = useSelector(state => state.userlist.userList)
    const supplierList = useSelector(state => state.userlist.supplierList)
    const admin_data = useSelector((state) => state.user.admindetail);
    const login_data = useSelector((state) => state.user.userdetail);

    const [assobj, setAssObj] = useState({ supplierId: null, auditor_ids: [], assessmentStartDate: null, assessmentEndDate: null, auditStartDate: null, auditEndDate: null, srfId: null })
    const [asssupplierobj, setAssSupplierObj] = useState({ supplierId: null })
    const [supplierlist, setSupplierList] = useState([])
    const [auditorlist, setAuditorList] = useState([])
    const [adddialog, setAddDialog] = useState(false)
    const [responseDialog, setResponseDialog] = useState(false)
    const [submitted, setSubmitted] = useState(false)
    const [supplierdialog, setSupplierDialog] = useState(false)
    const [isDialogVisible, setDialogVisible] = useState(false);
    const [selectedRow, setSelectedRow] = useState(null);
    const supplier = [
        {
            id: '240826-0058',
            supplierName: "Supplier A",
            questionary: "Questionary A",
            assessmentStartDate: "2024-10-01",
            assessmentEndDate: "2024-12-01",
            status: "Pending",
            score: 85
        },
        {
            id: '240826-0058',
            supplierName: "Supplier B",
            questionary: "Questionary B",
            assessmentStartDate: "2024-09-15",
            assessmentEndDate: "2024-11-15",
            status: "Completed",
            score: 90
        },
        {
            id: '240826-0058',
            supplierName: "Supplier C",
            questionary: "Questionary C",
            assessmentStartDate: "2024-08-20",
            assessmentEndDate: "2024-10-20",
            status: "In Progress",
            score: 75
        },
    ];
    const categoryOptions = [
        { id: 1, label: "Environment" },
        { id: 2, label: "Social" },
        { id: 3, label: "Governance" },
    ];

    const priorityOptions = [
        { id: 3, label: "High" },
        { id: 2, label: "Medium" },
        { id: 1, label: "Low" },
    ]
    const statusOptions = [
        { id: 1, label: "Not Started" },
        { id: 2, label: "In Progress" },
        { id: 3, label: "On Hold" },
        { id: 4, label: "Completed" },

    ]


    const [selectedAudit, setSelectedAudit] = useState(null)
    const [showModal, setShowModal] = useState(false)
    const [actionModal, setActionModal] = useState(false)
    const [actionModal2, setActionModal2] = useState(false)

    const [actionmodal, showActionModal] = useState(false)

    const viewAudit = (rowData) => {
        setSelectedAudit(rowData)
        setActionModal(true)
    }

    const forceUpdate = useForceUpdate()


    useEffect(() => {
        if (vendorCode?.id && admin_data?.id) {
            updateActions()
        }
    }, [vendorCode?.id, admin_data?.id])
    useEffect(() => {
        const handleFocus = () => {

            if (localStorage.getItem("reloadsaio")) {

                updateActions()

                // Clear the flag to avoid repeated execution
                localStorage.removeItem("reloadsaio");
            }
        };

        // Add event listener when component mounts
        window.addEventListener("focus", handleFocus);

        // Cleanup event listener when component unmounts
        return () => {
            window.removeEventListener("focus", handleFocus);
        };
    }, []);
    const getActionId = (item) => {
        if (item.categoryOfFinding === 1) {
            return 'GP' + item.id
        } else if (item.categoryOfFinding === 2) {
            return 'OFI' + item.id
        } else if (item.categoryOfFinding === 3) {
            if (item.nonComplianceType === 1) {
                return 'NCR' + item.id + ' (Major)'
            } else if (item.nonComplianceType === 2) {
                return 'NCR' + item.id + ' (Minor)'
            } else if (item.nonComplianceType === 3) {
                return 'NC' + item.id + ' (Major)'
            }
        }
    }
    const updateActions = () => {
        setLoad(true)
        setTakeDealerModal(false)
        setTakeActionModal(false)
        setShowSupplierActionPlanDialog(false)
        setShowDealerActionHistoryDialog(false)
        let locstring = {
            include: [
                {
                    relation: "locationTwos",
                    scope: { include: [{ relation: "locationThrees" }] },
                },
            ],
        };
        let uriString = {
            "where": {
                "vendorId": vendorCode.id
            },

            "include": [
                {
                    "relation": "supplierAssignmentSubmission"
                },
                {
                    "relation": "auditorAssignmentSubmission", scope: {
                        fields: { type: true, approved_on: true, approverComments: true, rejected_on: true, auditorMSIScore: true, submitted_on: true, modified_on: true, id: true },
                    }
                },
                {

                    "relation": "vendor"
                },
                {
                    "relation": "supplierActions",
                    scope: { include: ['supplierActionHistories'] }
                },
                {
                    "relation": "srf"
                }
            ]

        };

        const promise0 = []
        const promise1 = APIServices.get(API.SupplierAssessmentAss_Up_Global(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`)
        const promise2 = APIServices.get(
            API.AssignDCFClient_UP(admin_data.id)
        );
        const promise3 = APIServices.get(API.SRF)


        const promise4 = APIServices.get(API.GetRole_Up(admin_data.id))
        const promise5 = APIServices.get(API.SRF_Entity_User_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify({ include: ['srf'] }))}`)
        const promise6 = APIServices.get(
            API.LocationOne_UP(admin_data.id) +
            `?filter=${encodeURIComponent(JSON.stringify(locstring))}`
        );
        const promise7 = APIServices.get(API.ValueChainSubmission_UP(admin_data.id))

        const promise8 = APIServices.get(API.Actions)

        Promise.all([promise0, promise1, promise2, promise3, promise4, promise5, promise6, promise7, promise8]).then((values) => {


            let action = values[8].data.filter(item =>
                item.vendorId === vendorCode?.id?.toString() &&
                item.status === 'Initiated' &&
                ['Checklist Submission', 'Checklist Submission Returned'].includes(item.actionType)
            );
            let activeActionMaskIds = action.map(x => x.maskId)
            let archievedAction = values[8].data.filter(item =>
                item.vendorId === vendorCode?.id?.toString() &&
                (item.status === 'Completed' || item.status === 'Approved') && !activeActionMaskIds.includes(item.maskId)
            );
            const archieveDealerAction = flattenGroupedObject(groupArrayByKeys(archievedAction, ['maskId'])).map(x => ({ ...x, statusCode: 0, submittedDate: x.created, formType: 6, title: x.actionToBeTaken + ' - ' + x.maskId }))

            console.log(archievedAction)
            let srf_list = values[3].data
            let actions = values[1].data.filter(x => x.supplierActions && x?.auditorAssignmentSubmission?.type === 2).flatMap(x => x.supplierActions.filter(z => [1, 2, 3].includes(z.type)).map(y => ({ ...y, actionPlanApprovalData: DateTime.fromISO(x?.actionPlanApprovedDate || null, { zone: 'utc' }).setZone('Asia/Calcutta').toFormat('dd-LLL-yyyy'), date: x?.auditorAssignmentSubmission?.modified_on, msi: getMSIIDByAssignment(x) }))).filter(x => (x.categoryOfFinding === 3 || x.categoryOfFinding === 2)).map(x => ({ ...x, statusCode: x.type === 1 ? 7 : 0, title: (x.type === 1 ? 'Action for ' : 'View ') + (x.msi + ' ' + x.actionId), actionId: x.msi + ' ' + x.actionId, currentStatus: x.type !== 1 ? 'Submitted' : 'Not Started', dueDate: DateTime.fromISO(x.actionTargetDate, { zone: 'utc' }).toLocal().plus({ days: 2 }).toFormat('dd-LLL-yyyy'), formType: 5, timeLine: x.type !== 1 ? 'Submitted' : getOverdueDays(DateTime.fromISO(x.date, { zone: 'utc' }).toLocal().toFormat('LLL-yyyy')) >= 0 ? 'Overdue' : 'Due Soon' }))
            let actionPlan = values[1].data.filter(x => x.supplierActions && x?.auditorAssignmentSubmission?.type === 2 && Array.isArray(x.supplierActions) && x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)).length && x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)).every(z => (z.type === 12 || z.type === 102))).map((x) => ({ ...x, formType: 7, msiId: getMSIIDByAssignment(x), title: "Submit Action Plan for " + getMSIIDByAssignment(x), statusCode: 6, currentStatus: x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)).some(z => (z.type === 12 && z.actionPlanRejectedOn)) ? 'Returned' : x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)).every(z => (z.type === 12)) ? "Pending Submission" : "Draft", actions: x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)), dueDate: DateTime.fromISO(x?.auditorAssignmentSubmission?.approved_on, { zone: 'utc' }).toLocal().plus({ days: 7 }).toFormat('dd-LLL-yyyy'), timeLine: getOverdueDays(DateTime.fromISO(x?.auditorAssignmentSubmission?.approved_on, { zone: 'utc' }).toLocal().plus({ days: 7 }).toFormat('LLL-yyyy')) >= 0 ? 'Overdue' : 'Due Soon' }))
            let archivedActionPlan = values[1].data.filter(x => x.actionPlanType === 21 || x.actionPlanType === 1).map((x) => ({ ...x, formType: 7, msiId: getMSIIDByAssignment(x), title: "View Action Plan for " + getMSIIDByAssignment(x), statusCode: 0, currentStatus: "Submitted", actions: x.supplierActions.filter(y => (y.categoryOfFinding === 3 || y.categoryOfFinding === 2)), dueDate: '', timeLine: '', isApproved: x.actionPlanType === 1, submittedDate: x?.actionPlanSubmittedDate, submitted_on: DateTime.fromISO((x?.actionPlanApprovedDate || x?.actionPlanSubmittedDate), { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy'), dueDate: DateTime.fromISO(x?.auditorAssignmentSubmission?.approved_on, { zone: 'utc' }).toLocal().plus({ days: 7 }).toFormat('dd-LLL-yyyy') }))

            console.log(values[1].data.filter(x => x.actionPlanType === 21 || x.actionPlanType === 1))
            const shapedSite = values[6].data
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees &&
                                locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);
            setActionList(values[1].data.filter(i => i.supplierActions).flatMap(i => i.supplierActions))
            if (values[2].data.length && values[2].data[0].cf_ids && values[2].data[0].cf_ids.length) {
                let assSrfList = values[2].data[0].cf_ids.filter(i => srf_list.map(x => x.id).includes(i)).map(i => srf_list.find(x => x.id === i))
                setAssSrfList(assSrfList)
                console.log(assSrfList)
                setSupplierList(supplierList.filter(i => i.role === 'clientsupplier'))
                setAuditorList(userList.filter(i => values[4].data.some(x => x.user_id === i.id && x.roles && x.roles.includes(17))))

            }
            let assignment = [values[5].data.filter(i => i.srfId === 86 && i.type === 1 && i.reporter_ids.includes(vendorCode.id)).length ? {
                formType: 8, coverage: 'Corporate', srfId: 86
                , name: "Add / Update LCA Data Collection Form for parts supplied to TVS Motors", title: "Add / Update LCA Data Collection Form for parts supplied to TVS Motors", dueDate: null, currentStatus: 'NA', timeLine: "Due Now", statusCode: 6
            } : null].filter(x => x)

            for (const item of values[5].data.filter(i => i.srfId !== 86 && i.reporter_ids.includes(vendorCode.id))) {
                console.log(item)
                let months = getMonthsBetweenNew(
                    item.start_date,
                    item.end_date,
                    item.frequency === 4 ? 12 : item.frequency === 5 ? 6 : item.frequency,
                    0,
                    fymonth
                );

                for (const item2 of months) {

                    let found = values[7].data.find(x => x.srfId === item.srfId && x.entityUserAssId === item.id && x.vendorId === vendorCode.id &&
                        x.entityAssId === item.entityAssId &&
                        x.tier0_id === item.tier0_id &&
                        x.tier1_id === item.tier1_id &&
                        x.tier2_id === item.tier2_id &&
                        x.tier3_id === item.tier3_id && getRPTextFormat(x.reporting_period) === item2)
                    console.log(found, item, item2, vendorCode, values[7].data)
                    if (found) {
                        let type = found.type;
                        let reject = found.reject;
                        let status =
                            type === 0 && (!reject)
                                ? 0
                                : type === 0 && (reject)
                                    ? 1
                                    : type === 1 && reject
                                        ? 2
                                        : type === 1
                                            ? 3
                                            : type === 2
                                                ? 4
                                                : type === 3
                                                    ? 5
                                                    : null;
                        if (status === 0 || status === 1) {
                            status =
                                getOverdueDays(item2) >= 0
                                    ? 99
                                    : getOverdueDays(item2) >= -10
                                        ? 6 : getOverdueDays(item2) >= -15 ? 9
                                            : 7;
                        }
                        if (found.id === 226) {
                            console.log(status)
                        }
                        assignment.push({
                            coverage: getCoverageText(item, shapedSite), data: found, srfId: item.srfId, formType: 1, frequency: item.frequency, entityAssId: item.entityAssId, entityUserAssId: item.id, reporting_period: item2, self: item.reviewer_ids.length ? false : true, locationId: item.locationId, level: item.level,
                            reporting_period: item2, name: item.srf?.title, title: 'Submit ' + item.srf?.title + ' for the reporting period of ' + item2 || '', dueDate: getDueDate(item2), currentStatus: !type && reject ? "Returned" : 'Draft', timeLine: status === 6 ? 'Due Now' : status === 7 ? "Overdue" : status === 9 ? "Duesoon" : "Upcoming", statusCode: status
                        })
                    } else if (!found) {
                        let stat = getOverdueDays(item2) >= 0
                            ? 100
                            : getOverdueDays(item2) >= -10
                                ? 6 : getOverdueDays(item2) >= -15 ? 9
                                    : 7

                        assignment.push({
                            coverage: getCoverageText(item, shapedSite), srfId: item.srfId, formType: 1, frequency: item.frequency, entityAssId: item.entityAssId, entityUserAssId: item.id, reporting_period: item2, self: item.reviewer_ids.length ? false : true, locationId: item.locationId, level: item.level,
                            reporting_period: item2, name: item.srf?.title, title: 'Submit ' + item.srf?.title + ' for the reporting period of ' + item2 || '', dueDate: getDueDate(item2), currentStatus: 'Not Started', timeLine: stat === 6 ? 'Due Now' : stat === 7 ? "Overdue" : stat === 9 ? "Duesoon" : "Upcoming", statusCode: stat
                        })
                    }

                }
            }
            const filteredMSI = values[1].data.filter(x => (!x.supplierAssignmentSubmission || (x.supplierAssignmentSubmission && x.supplierAssignmentSubmission?.type === 0)))
            for (const x of filteredMSI) {
                let stat = getOverdueDaysByUTC(x.assessmentEndDate) >= 0
                    ? 100
                    : getOverdueDaysByUTC(x.assessmentEndDate) >= -10
                        ? 6 : getOverdueDaysByUTC(x.assessmentEndDate) >= -15 ? 9
                            : 7
                x.title = 'Submit MSI self-assessment ' + getMSIIDByAssignment(x)
                x.dueDate = getDate(x.assessmentEndDate, 'dd-LLL-yyyy')
                x.currentStatus = x?.supplierAssignmentSubmission ? 'Started' : 'Not Started'
                x.timeLine = stat === 6 ? 'Due Now' : stat === 9 ? "Due Soon" : stat === 100 ? "Upcoming" : "Overdue"
                x.formType = 2
                x.statusCode = stat
            }
            setAssignments(filteredMSI)
            setSubmittedAssessment(values[1].data.filter(x => (x?.supplierAssignmentSubmission && x?.supplierAssignmentSubmission?.type === 1)))
            // setSubmittedAssessment(values[1].data)

            setSubmissionData([...archieveDealerAction, ...archivedActionPlan.filter(x => x.statusCode === 0), ...actions.filter(x => x.statusCode === 0), ...assignment.filter(x => ![6, 7, 9, 99, 100, 101].includes(x.statusCode))])
            console.log(assignment.filter(x => [6, 7, 9].includes(x.statusCode)), assignment)
            console.log(archivedActionPlan)
            let att = []

            setData([...att, ...actionPlan.filter(x => x.statusCode === 6), ...actions.filter(x => x.statusCode === 7), ...assignment.filter(x => ([6, 7, 9, 101].includes(x.statusCode))), ...filteredMSI, ...action])


        }).then((res) => {
            setLoad(false)
        })
    }
    function flattenGroupedObject(groupedObj) {
        return Object.entries(groupedObj).map(([key, arr]) => {
            const lastItem = arr[arr.length - 1];
            return {
                ...lastItem,
                history: arr,
            };
        });
    }
    const getMSIIDByAssignment = (item) => {

        return 'MSI-' + item?.vendor?.code + '-' + DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')
    }
    const msiIdTemplate = (rowData) => {
        return (<div>{getMSIIDByAssignment(rowData)} </div>)
    }
    const reportTemplate = (rowData) => {
        return (rowData?.auditorAssignmentSubmission?.type === 2 ? <div onClick={() => { setSelectedAudit(rowData); setReportDialog(true); }}>View  </div> : 'NA')
    }
    const statusTemplate = (rowData) => {
        return (<div> {!rowData.auditStartDate ? 'Audit Not Scheduled' : !rowData.auditorAssignmentSubmission ? 'Audit Not Started' : rowData.auditorAssignmentSubmission?.type ? 'Audit Complete' : 'Audit Onprogress'} </div>)
    }
    const supplierScoreTemplate = (rowData) => {
        let finished = rowData?.supplierAssignmentSubmission?.type === 1
        console.log(rowData)

        return (
            <div className={finished ? 'clr-navy fw-6 fs-14 cur-pointer text-underline' : 'fw-5 fs-14'} onClick={(e) => { e.preventDefault(); if (finished) { setSelectedAudit(rowData); setActionModal(true) } }}>
                {rowData?.supplierAssignmentSubmission?.supplierMSIScore ? rowData?.supplierAssignmentSubmission?.supplierMSIScore : 'NA'}
            </div>
        );

    }
    const auditorScoreTemplate = (rowData) => {
        let finished = rowData?.auditorAssignmentSubmission?.type === 1 && rowData?.assessmentEndDate


        return (
            <div className={finished ? 'clr-navy fw-6 fs-14 cur-pointer text-underline' : 'fw-5 fs-14'} onClick={(e) => { e.preventDefault(); if (finished) { setSelectedAudit(rowData); setActionModal2(true) } }}>
                {rowData?.auditorAssignmentSubmission?.auditorMSIScore ? rowData?.auditorAssignmentSubmission?.auditorMSIScore : 'NA'}
            </div>
        );

    }
    const findingTemplate = (rowData) => {
        return <>{rowData?.supplierActions?.length || '-'}</>
    }
    const getDueDate = (dateStr) => {
        const [from, to] = dateStr.split(" to ");
        if (to) {
            return DateTime.fromFormat(to, "LLL-yyyy").plus({ month: 1 }).startOf('month').toFormat('dd-LLL-yyyy');
        }
        return DateTime.fromFormat(from, "LLL-yyyy").plus({ month: 1 }).startOf('month').toFormat('dd-LLL-yyyy')
    };

    const getCoverageText = (rowData, rawsitelist) => {
        let text = "";
        console.log(rowData);
        if (rowData.level === 0) {
            text = "Corporate";
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.locationId
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.locationId;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.level === 3) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.locationId;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };
    function getOverdueDays(monthString) {
        console.log(monthString);
        const [startMonth, endMonth] = monthString.split(" to ");

        const month = endMonth ? endMonth : startMonth;
        const [monthValue, year] = month.split("-");
        const endOfMonth = DateTime.fromObject({
            year: parseInt(year),
            month: DateTime.fromFormat(monthValue, "LLL").month,
        }).endOf("month");
        const currentDate = DateTime.local();
        console.log(month, endOfMonth.diff(currentDate, "days").days);
        return endOfMonth.diff(currentDate, "days").days;
    }
    function getOverdueDaysByUTC(monthString) {

        const endOfMonth = DateTime.fromISO(monthString, { zone: 'Asia/Calcutta' })
        const currentDate = DateTime.local();

        return endOfMonth.diff(currentDate, "days").days;
    }



    const idTemplate = (rowData) => {
        return (
            // <a
            //     href="#"
            //     onClick={() => openDialog(rowData)} // Trigger dialog open
            //     style={{ textDecoration: 'none', color: '#007bff' }}
            // >
            //     {rowData.id}
            // </a>

            <div className='maskid' onClick={() => viewAudit(rowData)}>
                {rowData.id}
            </div>
        );
    };


    const getDate = (date, format) => {

        if (!date) {
            return 'Not Set'
        } if (typeof date === 'string') {
            return DateTime.fromISO(date, { zone: 'utc' }).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        } else if (DateTime.isDateTime(date)) {
            return date.toFormat(format ? format : 'dd-MM-yyyy')
        } else {
            return DateTime.fromJSDate(date).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        }

    };
    const dateTemplate = (date) => {
        console.log(getDate(date))
        return getDate(date)

    }
    const getSupplier = (id) => {

        let user_name = 'Not Found'
        if (id === admin_data.id) {
            return 'Enterprise Admin'
        }
        let index = supplierList.findIndex(i => i.id === id)
        if (index !== -1) {
            user_name = supplierList[index].information.supplierName
        }
        return user_name

    }
    const resendMail = (rowData) => {
        let assessment = assignments.find(i => i.id === activeAss.id)
        let txt = supplierList.find(i => rowData.supplierId === i.id)
        if (assessment) {
            let body = `<p>Hi ${txt.information.empname}</p>  <p>You have received mail in order to fill your Questionary Response, <a href="${window.location.origin}/supplier/assessment/${rowData.id}">click here </a> to open Supplier Assessment Questionary Form</p><hr/><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
            APIServices.post(API.SubmissionMail, { email: [txt.email], subject: 'Supplier Assessment Form - ' + assessment.title, body: body }).then(res => {
                Swal.fire({
                    position: "center",
                    icon: "warning",
                    title: "Mail Sent Successfully",
                    showConfirmButton: false,
                    timer: 1500,
                });
            })

        }


    }

    const onCheckBoxSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = !items.selected
            }
        })
        forceUpdate()
    }
    const onRadioButtonSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = true
            } else {
                items.selected = false
            }
        })
        forceUpdate()
    }
    const onDateSelected = (item, val) => {

        item.value = val;
        forceUpdate()
    }
    const onNumberChange = (item, val, nan) => {
        console.log(item, val)

        item.value = val;


        forceUpdate()
    }
    const onCommentChange = (item, val, nan) => {

        item.comment = val;
        forceUpdate()
    }
    const onChangeDropwdown = (item, val) => {
        item.value = val;
        console.log(val)
        item.values.forEach((i) => {
            if (i.value === val) {
                i.selected = true
            } else {
                i.selected = false
            }
        })
        forceUpdate()
    }
    const renderItems = (item, index) => {
        const getLabel = (item) => item.label.replace(/(<([^>]+)>)/gi, "").replace(/\n/g, " ").replace(/&nbsp;/g, " ").replace('&amp;', '&');

        return (
            <div key={index} className="form-row grid m-0 p-fluid" style={{ padding: 10, border: item.error === 1 ? '1px solid red' : 'none' }}>
                <Tooltip target={`.tooltip-${index}`} position="top" />

                {/* Query Column */}
                <label className="col-4 fs-16 fw-5 text-justify">
                    {getLabel(item)}
                    {item.required && <span className="mandatory mr-2">*</span>}
                    {item.description && <i className={`material-icons fs-14 tooltip-${index}`} data-pr-tooltip={item.description}>help</i>}
                </label>

                {/* Response Column */}
                <div className="col-4">
                    {item.type === 'checkbox-group' && item.values.map((cb, cbind) => (
                        <div className="flex align-items-center" key={cbind}>
                            <Checkbox disabled={show} inputId={`cb-${index}-${cbind}`} name={cb.label} value={cb.value} onChange={(e) => onCheckBoxSelected(item, cbind)} checked={cb.selected} />
                            <label htmlFor={`cb-${index}-${cbind}`} className="ml-2">{cb.label}</label>
                        </div>
                    ))}

                    {item.type === 'date' && (
                        <Calendar disabled={show} value={item.value ? new Date(item.value) : null} onChange={(e) => onDateSelected(item, e.value)} />
                    )}

                    {item.type === 'number' && (
                        <InputNumber disabled={show} value={item.value} onChange={(e) => onNumberChange(item, e.value)} />
                    )}

                    {item.type === 'radio-group' && item.values.map((rb, rbindex) => (
                        <div className="flex align-items-center" key={rbindex}>
                            <RadioButton disabled={show} inputId={`rb-${index}-${rbindex}`} name={rb.label} value={rb.value} onChange={(e) => onRadioButtonSelected(item, rbindex)} checked={rb.selected === true} />
                            <label htmlFor={`rb-${index}-${rbindex}`} className="ml-2">{rb.label}</label>
                        </div>
                    ))}

                    {item.type === 'select' && (
                        <Dropdown disabled={show} value={item.value} options={item.values} optionLabel="label" onChange={(e) => onChangeDropwdown(item, e.value)} />
                    )}

                    {item.type === 'textarea' && (
                        <Editor disabled={show} value={item.value} onTextChange={(e) => onNumberChange(item, e.htmlValue)} />
                    )}

                    {item.type === 'text' && (
                        <InputText disabled={show} value={item.value} onChange={(e) => onNumberChange(item, e.target.value)} />
                    )}
                </div>

                {/* Comments Column */}
                <div className="col-4">
                    <InputText disabled={show} value={item.comments} onChange={(e) => onCommentChange(item, e.target.value)} placeholder="Add comments" />
                </div>
            </div>
        );
    };

    const questionaryTemplate = (rowData) => {

        let txt = asssrflist.find(i => rowData.srfId === i.id)
        console.log(txt)
        return (<>{!txt ? '' : txt.title} </>)

    }

    const openDialog = (rowData) => {
        setSelectedRow(rowData); // Store the clicked row's data
        setDialogVisible(true);  // Show the dialog
    };

    // Function to close the dialog
    const hideDialog = () => {
        setDialogVisible(false);
        setSelectedRow(null); // Clear the selected row
    };
    const updateAssessment = (val) => {
        setSelectedAudit(val);
        let loc = JSON.parse(JSON.stringify(assignments))
        let index = loc.findIndex(i => i.id === val.id)
        if (index) {
            loc[index].supplierAssignmentSubmission = val.supplierAssignmentSubmission;
            setAssignments(loc)
            forceUpdate()
        }
    }
    const footer = () => {
        return (
            <div className='flex col-12 justify-content-end'>
                <Button label='Save & Exit' onClick={() => { handleSubmit() }} />
            </div>
        )
    }
    const handleSubmit = async (e) => {
        setSubmitted(true)
        let loc = JSON.parse(JSON.stringify(actionlist))
        let obj = { status: currentFormData.status, targetDate: currentFormData.targetDate }
        if (obj.status && obj.targetDate) {
            if (currentFormData.id) {
                let newObj = { modified_by: login_data.id, modified_on: DateTime.utc() }
                APIServices.patch(API.SupplierAction_Edit(currentFormData.id), { ...newObj, ...obj }).then((res) => {
                    let index = loc.findIndex(i => i.id === currentFormData.id)
                    if (index !== -1) {
                        loc[index] = { ...currentFormData, ...newObj, ...obj }
                        setActionList(loc)
                    }
                    setSubmitted(false)
                    showActionModal(false)
                })
            }
        }

    };
    const categoryTemplate = (option) => {
        return categoryOptions.find(c => c.id === option.category)?.label || 'Not Found';
    };
    const statusTemplate_ = (option) => {
        return statusOptions.find(c => c.id === option.status)?.label || 'Not Found';
    };
    const priorityTemplate = (option) => {
        return priorityOptions.find(c => c.id === option.priority)?.label || 'Not Found';
    };
    const sNoTemplate = (rowData, { rowIndex }) => <span className="cur-pointer clr-navy text-underline" onClick={() => { editAction(rowData) }}>{rowData.title}</span>;

    const editAction = (rowData) => {
        let loc = JSON.parse(JSON.stringify(rowData))
        if (loc.dueDate) {
            loc.targetDate = DateTime.fromISO(loc.targetDate, { zone: 'utc' }).toLocal().toJSDate()
        }
        setCurrentFormData(loc)
        showActionModal(true)

    }
    const entityRowFilterTemplate = (options) => {
        let allentity = JSON.parse(JSON.stringify(data));
        let allOptions = allentity.map((i) => i.coverage);
        let entOptions = [];
        allOptions.forEach((i) => {
            if (!entOptions.includes(i)) {
                entOptions.push(i);
            }
        });

        console.log(allentity);
        return (
            <MultiSelect
                panelClassName={"hidefilter"}
                value={options.value}
                options={entOptions}
                filter
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const dcfRowFilterTemplate = (options) => {
        let allentity = JSON.parse(JSON.stringify(data));

        let IdOptions = allentity.map((i) => ({ title: i.title || 'Not Found', id: i.srfId })).filter(
            (item, index, self) =>
                index ===
                self.findIndex(
                    (t) => t.title === item.title
                )
        );

        console.log(allentity);
        return (
            <MultiSelect
                panelClassName={"hidefilter"}
                filter
                value={options.value}
                options={IdOptions}
                optionValue="id"
                optionLabel="title"
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const statusBodyTemplate = (rowData) => {
        console.log(rowData)
        switch (rowData.status) {
            case 6:
                return <Badge severity="warning" value="Due Now" />;
            case 7:
                return <Badge severity="danger" value="Overdue" />;
            case 9:
                return <Badge severity="info" value="Due Soon" />;
            default:
                return <Badge severity="info" value="Upcoming" />;
        }
    };
    const statusRowFilterTemplate = (options) => {
        let allStatusOptions = [

            { name: "Submissions Overdue", id: 7 },
            { name: "Submission Due", id: 6 },
            { name: "Due Soon", id: 9 }


        ];

        return (
            <MultiSelect
                panelClassName={"hidefilter"}
                value={options.value}
                options={allStatusOptions}
                optionLabel="name"
                optionValue="id"
                filter
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const actionBodyTemplate = (rowData) => {

        return (<div className="fw-7 fs-16 clr-navy cur-pointer" onClick={() => { window.open(window.location.origin + '/cf_preview_view').sessionStorage.setItem('cfpreview', rowData.srfId) }}> {rowData.title || 'NA'} </div>)
    };
    const rpRowFilterTemplate = (options) => {
        let allentity = JSON.parse(JSON.stringify(data));
        let allOptions = allentity.map((i) => i.reporting_period);
        let entOptions = [];
        allOptions.forEach((i) => {
            if (!entOptions.includes(i)) {
                entOptions.push(i);
            }
        });

        console.log(allentity);
        return (
            <MultiSelect
                panelClassName={"hidefilter"}
                value={options.value}
                options={entOptions}
                filter
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const assessmentHeader = (item) => {
        return (
            <div className="flex align-items-center justify-content-between gap-2 w-full">
                <div className="font-bold ">{'MSI-' + login_data?.supplierCode + '-' + DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}</div>
                <div >Due On : <span className="font-bold">{DateTime.fromISO(item.assessmentEndDate, { zone: 'utc' }).toLocal().toFormat('dd-M-yyyy')}</span> </div>

                <div >Status : <tag className="status-tag-orange">Not Started</tag> </div>
            </div>

        )

    }

    const takeActionSupplier = (data) => {

        console.log(data)

        setTakeActionModal(true)
        setTakeActionData(data)

    }
    const displayDealerAction = (data) => {

        console.log(data)

        setShowDealerActionHistoryDialog(true)
        setDealerActionHistoryData(data)

    }
    const takeSupplierActionPlan = (data) => {

        console.log(data)

        setShowSupplierActionPlanDialog(true)
        setSupplierActionPlanData(data)

    }
    const takeActionControl = (rowData) => {
        setTakeDealerModal(true)
        setTakeActionDealer(rowData)
    }

    const actionTemplate = (rowData) => {

        if (rowData.actionType === "Checklist Submission" || rowData.actionType === "Checklist Submission Returned") {
            console.log(rowData, 'rowData.actionType)')
            return <div className="table-link-clickable" onClick={() => takeActionControl(rowData)} >{rowData.actionToBeTaken} - {rowData.maskId}  </div>
        }

        switch (rowData.formType) {

            case 1:


                return <div className="table-link-clickable" onClick={() => {
                    // window.open(window.location.origin + '/cf_preview_view').sessionStorage.setItem('cfpreview', rowData.srfId)
                    rowData.srfId !== 86 &&
                        window.open(window.origin + '/srf_input_entry').sessionStorage.setItem('srfsubmission', JSON.stringify({ id: rowData.srfId, submitId: rowData?.data?.id || null, params: { state: { data: { supplierId: login_data.id, vendorId: vendorCode?.id, vendorCode: vendorCode?.code, frequency: rowData.frequency, entityUserAssId: rowData.entityUserAssId, self: rowData.self, entityAssId: rowData.entityAssId, locationId: rowData.locationId, level: rowData.level, reporting_period: rowData.reporting_period }, status: true } } }))
                }} >{rowData.title} </div>

            case 2:
                return <div className="table-link-clickable" onClick={() => { viewAudit(rowData) }} >{rowData.title}  </div>
            case 5:
                return <div className="table-link-clickable" onClick={() => { takeActionSupplier(rowData) }} >{rowData.title}  </div>
            case 7:
                return <div className="table-link-clickable" onClick={() => { takeSupplierActionPlan(rowData) }} >{rowData.title}  </div>
            case 8:
                return <div className="table-link-clickable" onClick={() => {
                    // window.open(window.location.origin + '/cf_preview_view').sessionStorage.setItem('cfpreview', rowData.srfId)
                    rowData.srfId === 86 && window.open(window.origin + '/lca_input_entry').sessionStorage.setItem('srfsubmission', JSON.stringify({ id: rowData.srfId, submitId: rowData?.data?.id || null, params: { state: { data: { supplierId: login_data.id, vendorId: vendorCode?.id, vendorCode: vendorCode?.code, frequency: rowData.frequency, entityUserAssId: rowData.entityUserAssId, self: rowData.self, entityAssId: rowData.entityAssId, locationId: rowData.locationId, level: rowData.level, reporting_period: rowData.reporting_period }, status: true } } }))
                }} >{rowData.title} </div>

            case 0:
                return <div className="table-link-clickable" onClick={() => {

                    window.open(window.origin + '/lca')
                }} >{rowData.title} </div>
            default: return;
        }
    }
    const archieveActionTemplate = (rowData) => {

        switch (rowData.formType) {

            case 1:

                console.log(rowData)
                return <div className="table-link-clickable" onClick={() => {
                    // window.open(window.location.origin + '/cf_preview_view').sessionStorage.setItem('cfpreview', rowData.srfId)

                    window.open(window.origin + '/srf_input_entry').sessionStorage.setItem('srfsubmission', JSON.stringify({ id: rowData.srfId, submitId: rowData?.data?.id || null, params: { state: { data: { supplierId: login_data.id, vendorId: vendorCode?.id, vendorCode: vendorCode?.code, frequency: rowData.frequency, entityUserAssId: rowData.entityUserAssId, self: rowData.self, entityAssId: rowData.entityAssId, locationId: rowData.locationId, level: rowData.level, reporting_period: rowData.reporting_period }, status: true } } }))
                }} >{rowData.title} </div>

            case 5:
                return <div className="table-link-clickable" onClick={() => { takeActionSupplier(rowData) }} >{rowData.title}  </div>
            case 6:
                return <div className="table-link-clickable" onClick={() => { displayDealerAction(rowData) }} >{rowData.title}  </div>

            case 7:
                return <div className="table-link-clickable" onClick={() => { takeSupplierActionPlan(rowData) }} >{rowData.title}  </div>

            default:
                return <></>

        }
    }
    // Category mapping based on formType
    const getCategoryByFormType = (formType) => {
        switch (formType) {
            case 1:
                return 'Form Submission';
            case 2:
                return 'MSI Self Assessment';
            case 5:
                return 'MSI Supplier Action';
            case 6:
                return 'MSI Dealer Action';
            case 7:
                return 'MSI Action Plan';
            case 8:
                return 'LCA';
            default:
                return 'Unknown';
        }
    };

    // FormType category template function
    const formTypeCategoryTemplate = (rowData) => {
        return (
            <div>
                {getCategoryByFormType(rowData.formType)}
            </div>
        );
    };

    // Category filter template function
    const categoryFilterTemplate = (options) => {
        const categoryOptions = [
            { name: 'Form Submission', value: 1 },
            { name: 'MSI Self Assessment', value: 2 },
            { name: 'MSI Supplier Action', value: 5 },
            { name: 'MSI Dealer Action', value: 6 },
            { name: 'MSI Action Plan', value: 7 },
            { name: 'LCA', value: 8 }
        ];

        // Get unique formTypes from current data
        const availableFormTypes = Array.from(new Set(data.map(item => item.formType)));
        const filteredOptions = categoryOptions.filter(option => availableFormTypes.includes(option.value));

        return (
            <MultiSelect
                value={options.value}
                options={filteredOptions}
                optionLabel="name"
                optionValue="value"
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const RowFilterTemplate = (options, obj) => {
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(data.map((i) => i[obj])))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const timelineTemplate = (rowData) => {
        console.log(rowData)
        switch (rowData.statusCode) {
            case 6:
                return <Badge severity="warning" value="Due Now" />;
            case 7:
                return <Badge severity="danger" value="Overdue" />;
            case 9:
                return <Badge severity="info" value="Due Soon" />;
            case 101:
                return <Badge severity="info" value="No Due" />;
            default:
                return <Badge severity="info" value="Upcoming" />;

        }
    };

    const currentStatusTemplate = (rowData) => {
        if (rowData.status === 'Initiated') {
            return <Tag value={'Pending'} />
        }

        return <Tag value={rowData.currentStatus} />

    };

    const footerTemplate = (
        <></>
    );

    const dueDateBodyTemplate = (rowData) => {
        console.log(rowData, ' R D')
        if (!rowData || !rowData.dueDate) {
            return '';
        }

        // Attempt to parse the date
        const parsedDate = new Date(rowData.dueDate);

        // If date is invalid, just return the original value
        if (isNaN(parsedDate)) {
            return rowData.dueDate;
        }

        // Format the date as DD-MM-YYYY
        const day = String(parsedDate.getDate()).padStart(2, '0');
        const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
        const year = parsedDate.getFullYear();

        return `${day}-${month}-${year}`;
    };

    // Template for submitted_on column
    const submittedOnTemplate = (rowData) => {
        if (!rowData?.data?.submitted_on) {
            return 'Not Submitted';
        }
        return getDate(rowData.data.submitted_on, 'dd-MM-yyyy');
    };

    // Filter data based on date range
    useEffect(() => {
        let filtered = [...data];

        // Apply date range filter if both start and end dates are set
        if (dateFilter.start && dateFilter.end) {
            filtered = filtered.filter(rowData => {
                if (!rowData?.data?.submitted_on) return false;

                const itemDate = DateTime.fromISO(rowData.data.submitted_on, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        setFilteredData(filtered);
    }, [data, dateFilter]);

    function transformSupplierData(data) {

        return data.flatMap(item => {
            if (!item.vendorCodes || !Array.isArray(item.vendorCodes)) {
                // Handle undefined or null vendorCodes
                return null
            }
            const { vendorCodes, ...rest } = item
            // Transform vendorCodes into individual objects

            return item.vendorCodes.map(vendor => ({
                customId: vendor.userProfileId + '~' + vendor.code + '~' + vendor.id,
                customLabel: vendor.code + ' : ' + vendor?.supplierName,
                ...rest, vendorData: vendor,
                vendorId: vendor.id,
                supplierCode: vendor.code,
            }));
        }).filter(x => x)
    }
    function transformDealerData(data) {

        return data.flatMap(item => {
            if (!item.vendorCodes || !Array.isArray(item.vendorCodes)) {
                // Handle undefined or null vendorCodes
                return null
            }
            const { vendorCodes, ...rest } = item
            // Transform vendorCodes into individual objects

            return item.vendorCodes.map(vendor => ({
                customId: vendor.userProfileId + '~' + vendor.code + '~' + vendor.id,
                customLabel: vendor.code + ' : ' + vendor?.dealerName,
                ...rest,
                dealerCode: vendor.code,
                dealerCategory: vendor.dealerCategory,
            }));
        }).filter(x => x)
    }
    return (
        <div className="col-12">
            <div className="col-12">

                <div className="col-12 flex align-items-center" style={{ padding: '0px 20px' }}>
                    <span className="text-big-one"> Hello  {vendorCode?.supplierName || vendorCode?.dealerName} !  </span>  {login_data.email && <Badge className="ml-3 p-tag-blue flex align-items-center" value={vendorCode?.code}> </Badge>}


                </div>
                <div className="col-12" style={{ padding: '0px 20px' }} >
                    <div className="text-big-one text-navy flex fs-16">Thank you for being a trusted value chain partner of TVS Motors.
                    </div>
                    <div className="text-micro text-navy flex" style={{ flexDirection: 'column' }}>
                        <div>
                            At TVS Motors, we are dedicated to implementing sustainable business practices throughout our value chain. As part of this commitment, we require all our partners to align with our sustainability program,  <strong>My Sustainability Index (MSI)</strong>. This process involves completing a self-assessment checklist, followed by an on-site evaluation conducted by a qualified sustainability assessor. You will also need to address any areas identified during the assessment and submit your responses in the "My Actions" section below. Timely responses to these actions will lead to updates in your MSI scores and adjustments to your ranking accordingly.
                        </div> <br />
                        <div>  Additionally, TVS Motors is focused on reporting the Environmental, Social, and Governance (ESG) practices of our key value chain partners to meet various regulatory and sustainability standards. We are also committed to our <strong>Net Zero</strong> initiatives, which require us to monitor carbon emissions from materials, manufacturing and other operational processes including transportation, and across the value chain. To support this, we will need your periodic reporting on specific data collection requirements, which will also be listed in your “My Actions” section.
                        </div>  <br />
                        We appreciate your support in advancing TVS Motors’ sustainability goals. Thank you for your collaboration.</div>

                </div>

            </div>
            <div className="col-12">
                <TabMenu
                    className="roundedge"
                    model={[
                        { label: 'My Actions' },
                        vendorCode.supplierName && { label: 'MSI Reports' },
                        { label: 'Archived Data Submissions' },
                        vendorCode.dealerName && { label: 'Submitted MSI Reports' },
                        vendorCode.dealerName && { label: 'Submitted Self Assessment Reports' },
                    ].filter(Boolean)}
                    onTabChange={(e) => {
                        console.log(e);
                        setActiveIndex(e.index);
                        setActiveIndex2(e.value.label);
                    }}
                    activeIndex={activeindex}
                />

                {activeindex2 === "My Actions" && (
                    <div>
                        {/* Date Range Filter */}
                        <div className="col-12 flex justify-content-start align-items-end mb-3 gap-3">
                            <div className="flex flex-column">
                                <label className="mb-1">Submitted Date From</label>
                                <Calendar
                                    value={dateFilter.start}
                                    onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                                    placeholder="Start Date"
                                    dateFormat="dd-mm-yy"
                                    showIcon
                                />
                            </div>
                            <div className="flex flex-column">
                                <label className="mb-1">To</label>
                                <Calendar
                                    value={dateFilter.end}
                                    onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                                    placeholder="End Date"
                                    dateFormat="dd-mm-yy"
                                    showIcon
                                    minDate={dateFilter.start}
                                    disabled={!dateFilter.start}
                                />
                            </div>
                        </div>

                        <DataTable
                            loading={load}
                            scrollable
                            value={filteredData.length > 0 || (dateFilter.start && dateFilter.end) ? filteredData : data}
                            paginator
                            rows={10}
                            rowsPerPageOptions={[10, 20, 50, 100]}
                            filters={dataTableFilters}
                            onFilter={(e) => setDataTableFilters(e.filters)}
                        >
                             <Column
                                header="Category"
                                field="formType"
                                filter
                                showFilterMatchModes={false}
                                body={formTypeCategoryTemplate}
                                filterElement={categoryFilterTemplate}
                            />
                            <Column
                                header="Timeline"
                                field="timeLine"
                                filter
                                showFilterMatchModes={false}
                                body={timelineTemplate}
                                filterElement={(options) => RowFilterTemplate(options, "timeLine")}
                            />
                            <Column
                                header="Required Action"
                                field="title"
                                filter
                                showFilterMatchModes={false}
                                body={actionTemplate}
                                filterElement={(options) => RowFilterTemplate(options, "title")}
                            />
                           
                            <Column header="Due Date" field="dueDate" body={dueDateBodyTemplate} />
                            <Column header="Submitted On" field="data.submitted_on" body={submittedOnTemplate} />
                            <Column
                                header="Current Status"
                                field="currentStatus"
                                filter
                                showFilterMatchModes={false}
                                body={currentStatusTemplate}
                                filterElement={(options) => RowFilterTemplate(options, "currentStatus")}
                            />
                        </DataTable>
                    </div>
                )}

                {activeindex2 === "MSI Reports" && (
                    <div className="col-12">
                        <DataTable loading={load} value={submittedAssessment}>
                            <Column header="MSI ID" body={msiIdTemplate} />
                            <Column header="Self-Assessment Submission Date" body={(row) => getDate(row?.supplierAssignmentSubmission?.submitted_on)} />
                            <Column header="Status" body={statusTemplate} />
                            <Column header="MSI Self Assessment Score" field="supplierAssignmentSubmission.supplierMSIScore" body={supplierScoreTemplate} />
                            <Column header="MSI Calibration Dates" body={(row) => getDate(row.auditStartDate)} />
                            <Column header="MSI Score" field="auditorAssignmentSubmission.auditorMSIScore" body={auditorScoreTemplate} />
                            <Column header="Audit Report" body={reportTemplate} />
                            <Column header="Status of Findings" body={findingTemplate} />
                        </DataTable>
                    </div>
                )}

                {activeindex2 === "Archived Data Submissions" && (
                    <div className="col-12">
                        <DataTable
                            loading={load}
                            value={submissiondata.filter(x =>
                                [5, 6, 7].includes(x.formType) || (x.formType === 1 && x.data)
                            )}
                        >
                            <Column header="Required Action" field="title" body={archieveActionTemplate} />
                            <Column header="Reporting Period" field="reporting_period" />
                            <Column
                                header="Date of Submission"
                                body={(row) => (
                                    <>
                                        {getDate(
                                            row.submittedDate ||
                                            row?.data?.reporter_modified_on ||
                                            row?.supplierActionHistories?.at(-1)?.supplier_submitted_on
                                        )}
                                    </>
                                )}
                            />
                        </DataTable>
                    </div>
                )}




                {activeindex2 === "Submitted MSI Reports" && (
                    dealerData.length > 0 ? (
                        <DealersTableCompleted
                            data={dealerData.filter(
                                x => [1, 2].includes(x.dealerAuditorChecklistSubmission?.type)
                            )}
                            dealerList={transformSupplierData(dealerList)}
                            assessorList={userList}
                            globalFilter={globalFilter}
                        />
                    ) : (
                        <div>Loading dealer reports...</div>
                    )
                )}





                {activeindex2 === "Submitted Self Assessment Reports" && (
                    <DealersSelfAssessmentTable
                        data={dealerData}
                        dealerList={transformSupplierData(dealerList)}
                        assessorList={userList}
                        globalFilter={globalFilter}
                    />
                )}

                {/* Dialogs */}
                {selectedAudit && (
                    <Dialog
                        visible={actionModal}
                        header={`TVS Motors Supplier MSI Self-assessment (${getMSIIDByAssignment(selectedAudit)})`}
                        style={{ width: '75%' }}
                        onHide={() => handleModalClose(setActionModal, false)}
                    >
                        <SupplierPanel
                            editable={!selectedAudit.supplierAssignmentSubmission || selectedAudit?.supplierAssignmentSubmission?.type === 0}
                            vendorCode={vendorCode}
                            closeModal={(e) => { setActionModal(() => e); if (e === false) { handleModalClose(setActionModal, true); } }}
                            users={supplierList}
                            updateData={updateAssessment}
                            readOnly={true}
                            auditId={selectedAudit}
                        />
                    </Dialog>
                )}

                <Dialog visible={actionModal2} style={{ width: '75%' }} onHide={() => { handleModalClose(setActionModal2, false); setTimeout(() => setSelectedAudit(null), 500); }}>
                    <AuditPanel auditId={selectedAudit} editable={false} closeModal={setActionModal2} />
                </Dialog>

                <Dialog visible={takeActionModal} style={{ width: '75%' }} onHide={() => handleModalClose(setTakeActionModal, false)} header="Action Taken" footer={footerTemplate}>
                    <TakeActionSupplier data={takeActionData} refresh={updateActions} />
                </Dialog>

                <Dialog visible={actionmodal} style={{ width: '75%' }} footer={footer} header="Update Action" onHide={() => showActionModal(false)}>
                    <div className="p-fluid grid m-0 p-3">
                        {currentFormData.description && (
                            <div className="p-field col-12 font-italic">{currentFormData.description}</div>
                        )}
                        <div className="p-field col-6">
                            <label htmlFor="status">Select Status<span className="mandatory"> *</span></label>
                            <Dropdown
                                id="status"
                                className="mt-2"
                                value={currentFormData.status}
                                optionLabel="label"
                                optionValue="id"
                                options={statusOptions}
                                onChange={(e) => setCurrentFormData((prev) => ({ ...prev, status: e.value }))}
                                placeholder="Select Status"
                            />
                            {submitted && !currentFormData.status && (
                                <small className="p-invalid" style={{ color: "red" }}>Select Status</small>
                            )}
                        </div>
                        <div className="p-field col-6">
                            <label htmlFor="targetDate">Expected Completion Date<span className="mandatory"> *</span></label>
                            <Calendar
                                id="targetDate"
                                className="mt-2"
                                dateFormat="dd/m/yy"
                                minDate={new Date()}
                                value={currentFormData.targetDate}
                                onChange={(e) => setCurrentFormData((prev) => ({ ...prev, targetDate: e.value }))}
                                placeholder="Select Target Date"
                            />
                            {submitted && !currentFormData.targetDate && (
                                <small className="p-invalid" style={{ color: "red" }}>Set Target Date</small>
                            )}
                        </div>
                    </div>
                </Dialog>

                <Dialog visible={reportdialog} style={{ width: 750 }} onHide={() => handleModalClose(setReportDialog, false)}>
                    <SupplierReport report={selectedAudit} />
                </Dialog>

                <Dialog visible={takeDealerModal} style={{ width: 750 }} onHide={() => handleModalClose(setTakeDealerModal, false)}>
                    <TakeActionDealer data={takeActionDealer} refresh={updateActions} />
                </Dialog>

                <DealerActionHistoryDialog visible={showDealerActionHistoryDialog} onHide={() => setShowDealerActionHistoryDialog(false)} data={dealerActionHistoryData} />
                <SupplierActionPlanDialog visible={showSupplierActionPlanDialog} onHide={() => setShowSupplierActionPlanDialog(false)} data={supplierActionPlanData} refresh={updateActions} />
            </div>
        </div >
    )
}

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(SupplierHomeTVS, comparisonFn);