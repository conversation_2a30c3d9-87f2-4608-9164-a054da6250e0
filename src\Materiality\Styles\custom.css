.drag-container {
    display: flex;
    gap: 10px;
}

.drag-item {
    width: 100px;
    height: 100px;
    background-color: lightblue;
    text-align: center;
    line-height: 100px;
    cursor: pointer;
}
.react-datepicker-wrapper{
    display: flex;
}
.react-datepicker-wrapper .react-datepicker__input-container{
    display: flex;
    width: 100%;
}
.react-datepicker__input-container input{
    height:40px;
    width: 100%;
    background: #ffffff;
    border: 1px solid #ced4da;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
}
.react-datepicker__header{
    background: white;
}
.p-dropdown-items-wrapper {
    height: auto;
}
.p-tabmenu .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {
    background: #ffffff;
    border-color: #315975;
    color: #315975;
    padding: 5px;
}
.p-menuitem-text{
    font-size: 13px;
}
.p-ink {
    background-color: unset;
}
.user-data-table-height .p-datatable-wrapper{
    height: 370px;
}
.p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link:not(.p-disabled):focus {
    box-shadow: none;
}
.br-5 {
    border-radius: 5px;
}
.br-1 {
    border-radius: 10px;
}

.br-2 {
    border-radius: 20px;
}

.layout-topbar .custom-layout-topbar-menu {
    margin-left: 0;
    position: absolute;
    flex-direction: column;
    background-color: var(--surface-overlay);
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05), 0px 1px 4px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 1rem;
    right: 2rem;
    top: 5rem;
    min-width: 15rem;
    display: none;
    animation: scalein 0.15s linear;
}
.d-none {
    display: none !important;
}
.layout-topbar .layout-topbar-button span {
    font-size: 1rem;
    display: block;
}
.p-tabmenu .p-tabmenu-nav .p-tabmenuitem {
    margin-right: 0;
    justify-content: center;
    display: flex;
}
.p-checkbox .p-checkbox-box.p-highlight {
    border-color: #315975;
    background: #315975;
}
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    border-color: #315975;
    background: #315975;
    color: #ffffff;
}
.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #315975;
}
.p-radiobutton .p-radiobutton-box.p-highlight {
    border-color: #315975;
    background: #315975;
}
.p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
    border-color: #315975;
}
.p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    border-color: #315975;
    background: #315975;
    color: #ffffff;
}
.p-radiobutton .p-radiobutton-box:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}
.p-dropdown:not(.p-disabled):hover {
    border-color: #315975;
}
.p-dropdown:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}
.p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #31597510;
    border-color: #315975;
}
.p-inputtext:enabled:hover {
    border-color: #315975;
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.p-button:focus {
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #31597510, 0 1px 2px 0 black;
}
.p-button.p-button-text:enabled:hover, .p-button.p-button-text:not(button):not(a):not(.p-disabled):hover {
    background: #31597510;
    color: #315975;
    border-color: transparent;
}
.p-button.p-button-outlined:enabled:hover, .p-button.p-button-outlined:not(button):not(a):not(.p-disabled):hover {
    background:#31597510;
    color: #315975;
    border: 1px solid;
}
.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    color: #315975;
    background: #31597510;
}
.padding-8{
    padding: 8px;
}
.p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #315975;
}
.p-inputswitch.p-focus .p-inputswitch-slider {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 0.2rem #315975;
}
.p-inputswitch.p-inputswitch-checked:not(.p-disabled):hover .p-inputswitch-slider {
    background: #315975;
}
.swal2-container {
    z-index: 9999 !important;
  }

  /* .ql-link {
            display: none !important;
        } */

  .ql-clean {
    display: none !important;
  }

  .p-datatable-wrapper {
    height: 550px;
  }
  .ql-code-block {
    display: none !important;
  }
  .text-editor .ql-toolbar .ql-formats .ql-link {
    display: block !important;
  }
  .text-editor .ql-toolbar .ql-formats .ql-image {
    display: none !important;
  }

  .text-area .ql-toolbar .ql-formats .ql-header {
    display: none;
  }
  .text-area .ql-toolbar .ql-formats .ql-font {
    display: none !important;
  }
  .text-area .ql-toolbar .ql-formats .ql-link {
    display: block !important;
  }
  .text-area .ql-toolbar .ql-formats .ql-image {
    display: none !important;
  }
  .target-options .ql-toolbar .ql-formats .ql-header {
    display: none !important;
  }

  .target-options .ql-toolbar .ql-formats .ql-font {
    display: none !important;
  }
  .target-options .ql-toolbar .ql-formats .ql-link {
    display: none !important;
  }
  .nasdaq sectionheader1 {
    color: #005284;
    font-weight: 700;
    text-decoration: underline;
  }
  .gri sectionheader1 {
    color: #005284;
    font-weight: 700;
  }
  .gri .list {
    color: black;
    list-style-type: none;
    padding-right: 30px;
    text-align: justify;
  }

  .list label {
    display: block;
    margin-left: 30px;
    margin-bottom: 5px;
  }
  .draggable-widget {
    margin: 5px;
    padding: 10px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #374151;
    user-select: none;
  }

  .draggable-widget:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
  }

  .draggable-widget:active {
    cursor: grabbing;
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
  }

  .draggable-widget i {
    color: #6b7280;
    transition: color 0.3s ease;
  }

  .draggable-widget:hover i {
    color: #3b82f6;
  }

  /* Drop zone styles */
  .drop-zone-active {
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    border-radius: 8px;
    animation: pulse-border 1.5s infinite;
  }

  @keyframes pulse-border {
    0% {
        border-color: #2196f3;
        background-color: #e3f2fd;
    }
    50% {
        border-color: #1976d2;
        background-color: #bbdefb;
    }
    100% {
        border-color: #2196f3;
        background-color: #e3f2fd;
    }
  }

  .drop-indicator {
    position: relative;
  }

  .drop-indicator::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #2196f3;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .drop-indicator:hover::before {
    opacity: 1;
  }

  /* Widget panel styling */
  .widget-panel {
    background: #f8fafc;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #e5e7eb;
  }

  .widget-panel-title {
    font-weight: 600;
    color: #374151;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #e5e7eb;
  }

  .target-options .ql-toolbar .ql-formats .ql-image {
    display: none !important;
  }

  .target-options .ql-toolbar .ql-formats .ql-code-block {
    display: none !important;
  }

  .p-component .p-editor-container {
    margin-top: 10px;
  }

  .stickyToTopTableHeaders {
    position: sticky;
    background-color: #fff;
    z-index: 1100;
  }

  .frozen-header-table {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .table_63 {
    height: 63vh;
  }

  .hvef {
    height: 69vh !important;
    overflow: auto !important;
  }

  .p-datatable .p-datatable-footer {
    background: white !important;
    border: 0 !important;
    display: flex;
    justify-content: center;
  }
  .fullheight .p-datatable-wrapper {
    height: 100%;
  }
  .headercenter .p-column-header-content .p-column-title {
    width: 100%;
  }

  .gchart {
    width: 100%;
    height: 0;
    padding-bottom: 60%;
    /* Adjust the aspect ratio as needed */
    position: relative;
  }

  .vertical-line {
    border: none;
    border-left: 2px solid black;
    /* Customize the line color and width */
    height: auto;
    /* Customize the line height */
    margin: 0 10px;
    /* Customize the margin around the line */
  }

  .gchart > div {
    position: absolute;
    width: 100% !important;
    height: 100% !important;
  }
  td:has(> .cell-p-invalid) {
    border: 1px solid red !important;
  }
  td:has(> .cell-p-invalid-id) {
    border: 1px dashed red !important;
  }
  .chip-fw ul {
    width: 100%;
  }
  .actionbtn .pi {
    font-size: 12px;
  }
  .tag-tooltip .p-tooltip-text {
    background: white;
  }

  .form-group label {
    color: #005284 !important;
    /*you can change the color */
  }

  .form-wrap.form-builder .stage-wrap.empty {
    border: 3px dashed #005284 !important;
  }

  .form-wrap.form-builder .frmb-control li {
    box-shadow: inset 0 0 0 1px #005284 !important;
    overflow-x: scroll !important;
  }

  .my-custom-view-wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: "background 0.2s ease";
    min-height: 28px;
    color: black;
    background: white;
    display: flex;
    align-items: center;
  }

  .my-custom-view-wrapper-selected {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: "background 0.2s ease";
    min-height: 28px;
    background: gray;
    color: white;
    display: flex;
    align-items: center;
  }

  .p-multiselect-header {
    display: none !important;
  }

  .hidefilter .p-multiselect-header {
    display: flex !important;
  }

  .custom-edit-button {
    visibility: hidden;
  }

  /* hide list controls  */
  .pac-container {
    z-index: 9999;
  }

  .autocomplete_width {
    width: 100%;
  }
 